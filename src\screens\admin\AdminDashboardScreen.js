import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { useMockAuth } from '../../context/MockAuthContext';
import mockCallData from '../../data/mockCallData';

const AdminDashboardScreen = ({ navigation }) => {
  const { currentUser, signOut } = useMockAuth();
  const [callQueue, setCallQueue] = useState([]);
  const [activeIssues, setActiveIssues] = useState([]);
  const [resolvedIssues, setResolvedIssues] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    waitingCalls: 0,
    activeIssues: 0,
    resolvedToday: 0,
    escalatedIssues: 0,
  });

  // Load data on mount
  useEffect(() => {
    loadData();
    // Initialize mock data if needed
    mockCallData.initializeMockCallData();
  }, []);

  // Load all data
  const loadData = async () => {
    try {
      setRefreshing(true);

      // Get call queue
      const queue = await mockCallData.getCallQueue();
      setCallQueue(queue);

      // Get active issues
      const active = await mockCallData.getActiveIssues();
      setActiveIssues(active);

      // Get resolved issues
      const resolved = await mockCallData.getResolvedIssues();
      setResolvedIssues(resolved);

      // Calculate stats
      const today = new Date().toISOString().split('T')[0];
      const resolvedToday = resolved.filter(issue =>
        issue.timestamp.startsWith(today)
      ).length;

      const escalatedIssues = active.filter(issue => issue.escalated).length;

      setStats({
        waitingCalls: queue.length,
        activeIssues: active.length,
        resolvedToday,
        escalatedIssues,
      });

      setRefreshing(false);
    } catch (error) {
      console.error('Error loading data:', error);
      setRefreshing(false);
      Alert.alert('Error', 'Failed to load dashboard data');
    }
  };

  // Handle refresh
  const onRefresh = () => {
    loadData();
  };

  // Handle logout
  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await signOut();
              if (result.success) {
                // Explicitly navigate to Login screen
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Login' }],
                });
              } else {
                Alert.alert('Logout Failed', 'Could not log out. Please try again.');
              }
            } catch (error) {
              console.error('Error during logout:', error);
              Alert.alert('Error', 'An unexpected error occurred during logout.');
            }
          }
        },
      ]
    );
  };

  // Render a stat card
  const renderStatCard = (title, value, icon, color, onPress) => (
    <TouchableOpacity
      style={[styles.statCard, { borderLeftColor: color }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.statContent}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
      </View>
      <View style={[styles.statIconContainer, { backgroundColor: color }]}>
        <Icon name={icon} size={24} color={COLORS.white} />
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>Admin Dashboard</Text>
            <Text style={styles.headerSubtitle}>
              {currentUser?.department || 'Support Department'}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
          >
            <Icon name="logout" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Welcome Message */}
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>
            Welcome back, {currentUser?.name || 'Admin'}
          </Text>
          <Text style={styles.dateText}>
            {new Date().toLocaleDateString('en-ZA', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </Text>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          {renderStatCard(
            'Waiting Calls',
            stats.waitingCalls,
            'phone-in-talk',
            COLORS.primaryGreen,
            () => navigation.navigate('AdminCallQueue')
          )}
          {renderStatCard(
            'Active Issues',
            stats.activeIssues,
            'clipboard-list',
            COLORS.info,
            () => navigation.navigate('AdminIssues', { filter: 'active' })
          )}
          {renderStatCard(
            'Resolved Today',
            stats.resolvedToday,
            'check-circle',
            COLORS.success,
            () => navigation.navigate('AdminIssues', { filter: 'resolved' })
          )}
          {renderStatCard(
            'Escalated Issues',
            stats.escalatedIssues,
            'alert-circle',
            COLORS.error,
            () => navigation.navigate('AdminIssues', { filter: 'escalated' })
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsContainer}>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('AdminCallQueue')}
            >
              <Icon name="headset" size={24} color={COLORS.white} />
              <Text style={styles.quickActionText}>Manage Calls</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('AdminIssues')}
            >
              <Icon name="clipboard-text" size={24} color={COLORS.white} />
              <Text style={styles.quickActionText}>View Issues</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => Alert.alert('Reports', 'Reports feature coming soon')}
            >
              <Icon name="chart-bar" size={24} color={COLORS.white} />
              <Text style={styles.quickActionText}>Reports</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Calls */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Calls</Text>
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('AdminCallQueue')}
            >
              <Text style={styles.viewAllText}>View All</Text>
              <Icon name="chevron-right" size={16} color={COLORS.primaryGreen} />
            </TouchableOpacity>
          </View>

          {callQueue.length > 0 ? (
            callQueue.slice(0, 3).map((call, index) => (
              <TouchableOpacity
                key={call.id}
                style={styles.callItem}
                onPress={() => navigation.navigate('AdminCallDetails', { callId: call.id })}
              >
                <View style={styles.callInfo}>
                  <Text style={styles.callerName}>{call.userName}</Text>
                  <Text style={styles.callTime}>
                    {new Date(call.timestamp).toLocaleTimeString('en-ZA', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </Text>
                </View>
                <View style={styles.callStatus}>
                  <Text style={styles.queuePosition}>#{call.queuePosition}</Text>
                  <View style={styles.waitingIndicator}>
                    <Text style={styles.waitingText}>Waiting</Text>
                  </View>
                </View>
                <Icon name="chevron-right" size={20} color={COLORS.darkGray} />
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyStateContainer}>
              <Icon name="phone-off" size={40} color={COLORS.lightGray} />
              <Text style={styles.emptyStateText}>No calls in queue</Text>
            </View>
          )}
        </View>

        {/* Recent Issues */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Issues</Text>
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('AdminIssues')}
            >
              <Text style={styles.viewAllText}>View All</Text>
              <Icon name="chevron-right" size={16} color={COLORS.primaryGreen} />
            </TouchableOpacity>
          </View>

          {activeIssues.length > 0 ? (
            activeIssues.slice(0, 3).map((issue, index) => (
              <TouchableOpacity
                key={issue.id}
                style={styles.issueItem}
                onPress={() => navigation.navigate('AdminIssueDetails', { issueId: issue.id })}
              >
                <View style={styles.issueInfo}>
                  <Text style={styles.issueCategory}>{issue.category}</Text>
                  <Text style={styles.issueReference}>{issue.referenceNumber}</Text>
                </View>
                <View style={styles.issueStatus}>
                  <View style={[
                    styles.statusIndicator,
                    issue.escalated ? styles.escalatedStatus :
                    issue.status === 'in-progress' ? styles.inProgressStatus : styles.newStatus
                  ]}>
                    <Text style={styles.statusText}>
                      {issue.escalated ? 'Escalated' :
                       issue.status === 'in-progress' ? 'In Progress' : 'New'}
                    </Text>
                  </View>
                </View>
                <Icon name="chevron-right" size={20} color={COLORS.darkGray} />
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyStateContainer}>
              <Icon name="clipboard-off" size={40} color={COLORS.lightGray} />
              <Text style={styles.emptyStateText}>No active issues</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    opacity: 0.8,
  },
  logoutButton: {
    padding: SPACING.sm,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.md,
    paddingBottom: SPACING.xxl,
  },
  welcomeContainer: {
    marginBottom: SPACING.lg,
  },
  welcomeText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  dateText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginTop: SPACING.xs,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.lg,
  },
  statCard: {
    width: '48%',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    borderLeftWidth: 4,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  statTitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginTop: 2,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginRight: 2,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 10,
    padding: SPACING.md,
    alignItems: 'center',
    width: '30%',
  },
  quickActionText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  callItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  callInfo: {
    flex: 1,
  },
  callerName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  callTime: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  callStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  queuePosition: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginRight: SPACING.sm,
  },
  waitingIndicator: {
    backgroundColor: '#FFF8E1',
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: 4,
  },
  waitingText: {
    fontSize: FONT_SIZES.xs,
    color: '#FFA000',
  },
  issueItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  issueInfo: {
    flex: 1,
  },
  issueCategory: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  issueReference: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  issueStatus: {
    marginRight: SPACING.md,
  },
  statusIndicator: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: 4,
  },
  newStatus: {
    backgroundColor: '#E3F2FD',
  },
  inProgressStatus: {
    backgroundColor: '#E8F5E9',
  },
  escalatedStatus: {
    backgroundColor: '#FFEBEE',
  },
  statusText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.lg,
  },
  emptyStateText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginTop: SPACING.sm,
  },
});

export default AdminDashboardScreen;
