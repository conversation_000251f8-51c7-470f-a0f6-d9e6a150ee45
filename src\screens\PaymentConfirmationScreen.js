import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Easing,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

const PaymentConfirmationScreen = ({ navigation, route }) => {
  // Get payment details from route params
  const { bill, refNumber, paymentDate } = route.params || {};
  
  // Animation values
  const checkmarkScale = new Animated.Value(0);
  const checkmarkOpacity = new Animated.Value(0);
  
  // Start animations when component mounts
  useEffect(() => {
    // Animate checkmark
    Animated.sequence([
      Animated.timing(checkmarkScale, {
        toValue: 1.2,
        duration: 400,
        easing: Easing.out(Easing.back(1.5)),
        useNativeDriver: true,
      }),
      Animated.timing(checkmarkScale, {
        toValue: 1,
        duration: 200,
        easing: Easing.inOut(Easing.ease),
        useNativeDriver: true,
      }),
    ]).start();
    
    // Fade in checkmark
    Animated.timing(checkmarkOpacity, {
      toValue: 1,
      duration: 400,
      useNativeDriver: true,
    }).start();
  }, []);
  
  // Format currency
  const formatCurrency = (amount) => {
    return `R ${amount.toFixed(2)}`;
  };
  
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const options = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Handle "Done" button press
  const handleDone = () => {
    // Navigate back to the dashboard
    navigation.navigate('Dashboard');
  };
  
  // Handle "View Receipt" button press
  const handleViewReceipt = () => {
    // In a real app, this would show a PDF receipt or navigate to a receipt screen
    alert('Receipt functionality will be implemented in a future update.');
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <View style={styles.headerPlaceholder} />
        <Text style={styles.headerTitle}>Payment Confirmation</Text>
        <TouchableOpacity 
          style={styles.closeButton}
          onPress={() => navigation.navigate('Dashboard')}
        >
          <Icon name="close" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Success Animation */}
        <View style={styles.successContainer}>
          <Animated.View 
            style={[
              styles.checkmarkCircle,
              {
                opacity: checkmarkOpacity,
                transform: [{ scale: checkmarkScale }]
              }
            ]}
          >
            <Icon name="check" size={60} color={COLORS.white} />
          </Animated.View>
          
          <Text style={styles.successTitle}>Payment Successful!</Text>
          <Text style={styles.successMessage}>
            Your payment has been processed successfully.
          </Text>
        </View>
        
        {/* Payment Details */}
        <View style={styles.detailsCard}>
          <View style={styles.detailsHeader}>
            <Icon name={getBillIcon(bill?.type)} size={24} color={COLORS.primaryGreen} />
            <Text style={styles.detailsTitle}>{bill?.type} Bill Payment</Text>
          </View>
          
          <View style={styles.detailsContent}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Reference Number:</Text>
              <Text style={styles.detailValue}>{refNumber}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Account Number:</Text>
              <Text style={styles.detailValue}>{bill?.accountNumber}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Amount Paid:</Text>
              <Text style={styles.detailAmount}>{formatCurrency(bill?.amount || 0)}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Payment Date:</Text>
              <Text style={styles.detailValue}>{formatDate(paymentDate)}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Payment Method:</Text>
              <Text style={styles.detailValue}>Credit Card (**** 1234)</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Status:</Text>
              <View style={styles.statusContainer}>
                <View style={styles.statusDot} />
                <Text style={styles.statusText}>Completed</Text>
              </View>
            </View>
          </View>
        </View>
        
        {/* What's Next Section */}
        <View style={styles.nextStepsCard}>
          <Text style={styles.nextStepsTitle}>What's Next?</Text>
          
          <View style={styles.nextStep}>
            <Icon name="email-outline" size={24} color={COLORS.primaryGreen} />
            <View style={styles.nextStepContent}>
              <Text style={styles.nextStepTitle}>Email Confirmation</Text>
              <Text style={styles.nextStepText}>
                A receipt has been sent to your registered email address.
              </Text>
            </View>
          </View>
          
          <View style={styles.nextStep}>
            <Icon name="history" size={24} color={COLORS.primaryGreen} />
            <View style={styles.nextStepContent}>
              <Text style={styles.nextStepTitle}>Payment History</Text>
              <Text style={styles.nextStepText}>
                This transaction will appear in your payment history.
              </Text>
            </View>
          </View>
          
          <View style={styles.nextStep}>
            <Icon name="account-check-outline" size={24} color={COLORS.primaryGreen} />
            <View style={styles.nextStepContent}>
              <Text style={styles.nextStepTitle}>Account Update</Text>
              <Text style={styles.nextStepText}>
                Your account will be updated within 24 hours.
              </Text>
            </View>
          </View>
        </View>
        
        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <CustomButton
            title="View Receipt"
            onPress={handleViewReceipt}
            style={styles.viewReceiptButton}
            type="outline"
            icon={<Icon name="file-document-outline" size={20} color={COLORS.primaryGreen} />}
          />
          
          <CustomButton
            title="Done"
            onPress={handleDone}
            style={styles.doneButton}
            icon={<Icon name="check-circle-outline" size={20} color={COLORS.white} />}
          />
        </View>
        
        {/* Support Note */}
        <View style={styles.supportNote}>
          <Text style={styles.supportText}>
            If you have any questions about this payment, please contact our support team.
          </Text>
          <TouchableOpacity>
            <Text style={styles.supportLink}>Contact Support</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Helper function to get icon based on bill type
const getBillIcon = (type) => {
  switch (type) {
    case 'Electricity':
      return 'flash';
    case 'Water':
      return 'water';
    case 'Property Tax':
      return 'home-city';
    case 'Waste Management':
      return 'delete';
    default:
      return 'receipt';
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
  },
  headerPlaceholder: {
    width: 24,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra padding for bottom tab bar
    alignItems: 'center',
  },
  successContainer: {
    alignItems: 'center',
    marginVertical: SPACING.xl,
  },
  checkmarkCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
    shadowColor: COLORS.success,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  successTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  successMessage: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
  },
  detailsCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    width: '100%',
  },
  detailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
    paddingBottom: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  detailsTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginLeft: SPACING.sm,
  },
  detailsContent: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  detailLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  detailValue: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  detailAmount: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.success,
    marginRight: SPACING.xs,
  },
  statusText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.success,
  },
  nextStepsCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    width: '100%',
  },
  nextStepsTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
  },
  nextStep: {
    flexDirection: 'row',
    marginBottom: SPACING.md,
  },
  nextStepContent: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  nextStepTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  nextStepText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: SPACING.md,
  },
  viewReceiptButton: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  doneButton: {
    flex: 1,
    marginLeft: SPACING.sm,
  },
  supportNote: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  supportText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  supportLink: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
  },
});

export default PaymentConfirmationScreen;
