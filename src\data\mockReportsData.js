// Mock data for doctor reports and analytics

// Patient visits data for the last 7 days
export const PATIENT_VISITS_DATA = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [{
    data: [12, 15, 8, 18, 22, 6, 4],
    color: (opacity = 1) => `rgba(46, 204, 113, ${opacity})`, // Green
    strokeWidth: 2
  }]
};

// Prescription trends for the last 6 months
export const PRESCRIPTION_TRENDS_DATA = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  datasets: [{
    data: [45, 52, 38, 67, 71, 58],
    color: (opacity = 1) => `rgba(52, 152, 219, ${opacity})`, // Blue
    strokeWidth: 2
  }]
};

// Appointment status distribution (pie chart data)
export const APPOINTMENT_STATUS_DATA = [
  {
    name: 'Completed',
    population: 68,
    color: '#388E3C', // Success green
    legendFontColor: '#333',
    legendFontSize: 14,
  },
  {
    name: 'Cancelled',
    population: 15,
    color: '#D32F2F', // Error red
    legendFontColor: '#333',
    legendFontSize: 14,
  },
  {
    name: 'No Show',
    population: 12,
    color: '#FFA000', // Warning orange
    legendFontColor: '#333',
    legendFontSize: 14,
  },
  {
    name: 'Rescheduled',
    population: 5,
    color: '#1976D2', // Info blue
    legendFontColor: '#333',
    legendFontSize: 14,
  }
];

// Common diagnoses data (bar chart)
export const COMMON_DIAGNOSES_DATA = {
  labels: ['Hypertension', 'Diabetes', 'Flu', 'Headache', 'Back Pain'],
  datasets: [{
    data: [25, 18, 15, 12, 8]
  }]
};

// Patient age distribution
export const PATIENT_AGE_DISTRIBUTION = [
  {
    name: '0-18',
    population: 15,
    color: '#1976D2', // Info blue
    legendFontColor: '#333',
    legendFontSize: 12,
  },
  {
    name: '19-35',
    population: 28,
    color: '#2E7D32', // Primary green
    legendFontColor: '#333',
    legendFontSize: 12,
  },
  {
    name: '36-50',
    population: 32,
    color: '#4CAF50', // Light green
    legendFontColor: '#333',
    legendFontSize: 12,
  },
  {
    name: '51-65',
    population: 18,
    color: '#FFA000', // Warning orange
    legendFontColor: '#333',
    legendFontSize: 12,
  },
  {
    name: '65+',
    population: 7,
    color: '#616161', // Dark gray
    legendFontColor: '#333',
    legendFontSize: 12,
  }
];

// Monthly revenue data
export const MONTHLY_REVENUE_DATA = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  datasets: [{
    data: [15000, 18500, 12000, 22000, 25500, 19800],
    color: (opacity = 1) => `rgba(155, 89, 182, ${opacity})`, // Purple
    strokeWidth: 2
  }]
};

// Summary statistics
export const SUMMARY_STATS = {
  totalPatients: 1247,
  totalAppointments: 3456,
  totalPrescriptions: 2891,
  averageWaitTime: 12, // minutes
  patientSatisfaction: 4.6, // out of 5
  monthlyGrowth: 8.5, // percentage
};

// Recent activity data
export const RECENT_ACTIVITIES = [
  {
    id: 1,
    type: 'appointment',
    description: 'Completed appointment with Sarah Johnson',
    time: '2 hours ago',
    icon: 'calendar-check',
    color: '#388E3C' // Success green
  },
  {
    id: 2,
    type: 'prescription',
    description: 'Prescribed medication for John Smith',
    time: '4 hours ago',
    icon: 'pill',
    color: '#1976D2' // Info blue
  },
  {
    id: 3,
    type: 'report',
    description: 'Generated monthly report',
    time: '1 day ago',
    icon: 'file-chart',
    color: '#FFA000' // Warning orange
  },
  {
    id: 4,
    type: 'patient',
    description: 'New patient registration: Mary Wilson',
    time: '2 days ago',
    icon: 'account-plus',
    color: '#2E7D32' // Primary green
  }
];

// Performance metrics
export const PERFORMANCE_METRICS = {
  appointmentsPerDay: 18.5,
  averageConsultationTime: 15, // minutes
  prescriptionAccuracy: 98.5, // percentage
  patientReturnRate: 85.2, // percentage
  onTimePerformance: 92.8, // percentage
};

// Chart configuration
export const CHART_CONFIG = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(46, 204, 113, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(51, 51, 51, ${opacity})`,
  style: {
    borderRadius: 16
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#2ECC71'
  }
};
