import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Animated,
  Dimensions,
  StatusBar,
  ImageBackground,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

const { width, height } = Dimensions.get('window');

const GOVERNMENT_SERVICES = [
  {
    id: '5',
    title: 'Vehicle Licensing',
    description: 'Register vehicles and renew licenses',
    icon: '🚗',
    category: 'transport',
    color: '#2E7D32',
    gradient: ['#2E7D32', '#4CAF50'],
  },
  {
    id: '7',
    title: 'Health Services',
    description: 'Book appointments and access health services',
    icon: '🏥',
    category: 'health',
    color: '#388E3C',
    gradient: ['#388E3C', '#66BB6A'],
  },
  {
    id: '8',
    title: 'RDP Housing',
    description: 'Apply for RDP housing and check application status',
    icon: '🏠',
    category: 'housing',
    color: '#43A047',
    gradient: ['#43A047', '#81C784'],
  },
];

const SERVICE_CATEGORIES = [
  { id: 'all', name: 'All Services', icon: '🏛️', color: '#1B5E20' },
  { id: 'transport', name: 'Transport', icon: '🚗', color: '#2E7D32' },
  { id: 'health', name: 'Health', icon: '🏥', color: '#388E3C' },
  { id: 'housing', name: 'Housing', icon: '🏠', color: '#43A047' },
];

const ServicesScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const scrollY = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Pulsing animation for search bar
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const filteredServices =
    selectedCategory === 'all'
      ? GOVERNMENT_SERVICES
      : GOVERNMENT_SERVICES.filter(service => service.category === selectedCategory);

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.9],
    extrapolate: 'clamp',
  });

  const headerScale = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.95],
    extrapolate: 'clamp',
  });

  const renderCategoryItem = ({ item, index }) => (
    <Animated.View
      style={[
        {
          transform: [
            {
              translateY: slideAnim.interpolate({
                inputRange: [0, 50],
                outputRange: [0, 50],
                extrapolate: 'clamp',
              }),
            },
          ],
          opacity: fadeAnim,
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.categoryItem,
          selectedCategory === item.id && {
            backgroundColor: item.color,
            transform: [{ scale: 1.1 }],
            elevation: 8,
            shadowOpacity: 0.3,
          },
        ]}
        onPress={() => setSelectedCategory(item.id)}
        activeOpacity={0.8}
      >
        <View style={[
          styles.categoryIconContainer,
          { backgroundColor: selectedCategory === item.id ? 'rgba(255,255,255,0.2)' : 'rgba(46, 125, 50, 0.1)' }
        ]}>
          <Text style={styles.categoryIcon}>{item.icon}</Text>
        </View>
        <Text
          style={[
            styles.categoryName,
            selectedCategory === item.id && styles.selectedCategoryName,
          ]}
        >
          {item.name}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderServiceItem = ({ item, index }) => (
    <Animated.View
      style={[
        styles.serviceCardContainer,
        {
          transform: [
            {
              translateY: slideAnim.interpolate({
                inputRange: [0, 50],
                outputRange: [0, 50 + (index * 10)],
                extrapolate: 'clamp',
              }),
            },
          ],
          opacity: fadeAnim,
        },
      ]}
    >
      <TouchableOpacity
        style={[styles.serviceCard, { borderLeftColor: item.color, borderLeftWidth: 4 }]}
        onPress={() => {
          if (item.id === '7') {
            navigation.navigate('HealthServices');
          } else if (item.id === '8') {
            navigation.navigate('RDPHousing');
          } else {
            alert(`${item.title} service details coming soon!`);
          }
        }}
        activeOpacity={0.9}
      >
        <View style={styles.serviceCardContent}>
          <View style={[styles.serviceIconContainer, { backgroundColor: item.color }]}>
            <Text style={styles.serviceIcon}>{item.icon}</Text>
          </View>
          <View style={styles.serviceTextContainer}>
            <Text style={styles.serviceTitle}>{item.title}</Text>
            <Text style={styles.serviceDescription}>{item.description}</Text>
          </View>
          <View style={[styles.serviceArrow, { backgroundColor: item.color }]}>
            <Text style={styles.arrowIcon}>→</Text>
          </View>
        </View>
        <View style={[styles.serviceGlow, { backgroundColor: item.color }]} />
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1B5E20" />
      
      {/* Animated Header */}
      <Animated.View
        style={[
          styles.headerContainer,
          {
            opacity: headerOpacity,
            transform: [{ scale: headerScale }],
          },
        ]}
      >
        <View style={styles.headerGradient}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Government Services</Text>
            <Text style={styles.headerSubtitle}>Your digital gateway to public services</Text>
          </View>
          <View style={styles.headerDecoration} />
        </View>
      </Animated.View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      >
        {/* Categories */}
        <Animated.View style={[styles.categoriesSection, { opacity: fadeAnim }]}>
          <Text style={styles.sectionTitle}>Browse Categories</Text>
          <FlatList
            data={SERVICE_CATEGORIES}
            renderItem={renderCategoryItem}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </Animated.View>

        {/* Search Bar */}
        <Animated.View
          style={[
            styles.searchSection,
            {
              transform: [{ scale: pulseAnim }],
              opacity: fadeAnim,
            },
          ]}
        >
          <TouchableOpacity
            style={styles.searchBar}
            onPress={() => alert('Search functionality coming soon!')}
            activeOpacity={0.8}
          >
            <View style={styles.searchIconContainer}>
              <Text style={styles.searchIcon}>🔍</Text>
            </View>
            <Text style={styles.searchPlaceholder}>Search for services...</Text>
            <View style={styles.searchGlow} />
          </TouchableOpacity>
        </Animated.View>

        {/* Services Grid */}
        <Animated.View style={[styles.servicesSection, { opacity: fadeAnim }]}>
          <Text style={styles.sectionTitle}>
            {selectedCategory === 'all'
              ? 'All Services'
              : SERVICE_CATEGORIES.find(cat => cat.id === selectedCategory)?.name}
          </Text>
          <View style={styles.servicesList}>
            {filteredServices.map((item, index) => (
              <View key={item.id}>
                {renderServiceItem({ item, index })}
              </View>
            ))}
          </View>
        </Animated.View>

        {/* Service Alerts */}
        <Animated.View
          style={[
            styles.alertSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.alertCard}>
            <View style={styles.alertHeader}>
              <View style={styles.alertIconContainer}>
                <Text style={styles.alertIcon}>🚨</Text>
              </View>
              <Text style={styles.alertTitle}>Service Alerts</Text>
            </View>
            <Text style={styles.alertText}>
              No current service alerts. Stay informed for any changes or updates on government services here.
            </Text>
            <View style={styles.alertGlow} />
          </View>
        </Animated.View>

        {/* Service Facilities */}
        <Animated.View
          style={[
            styles.facilitiesSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.facilitiesCard}>
            <View style={styles.facilitiesHeader}>
              <View style={styles.facilitiesIconContainer}>
                <Text style={styles.facilitiesIcon}>🏛️</Text>
              </View>
              <View>
                <Text style={styles.facilitiesTitle}>Service Facilities</Text>
                <Text style={styles.facilitiesSubtitle}>
                  Find government offices, clinics, and registration centers near you.
                </Text>
              </View>
            </View>
            <CustomButton
              title="Find Facilities"
              onPress={() => navigation.navigate('Facilities')}
              style={styles.facilitiesButton}
            />
          </View>
        </Animated.View>

        {/* Help Section */}
        <Animated.View
          style={[
            styles.helpSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.helpCard}>
            <View style={styles.helpHeader}>
              <Text style={styles.helpTitle}>Need Help?</Text>
              <Text style={styles.helpSubtitle}>
                Not sure which service you need? Chat with our Thusong AI assistant for guidance.
              </Text>
            </View>
            <CustomButton
              title="Chat with Thusong AI"
              onPress={() => navigation.navigate('ThusongAI')}
              style={styles.chatButton}
            />
            <View style={styles.helpDecoration} />
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F1F8E9',
  },
  headerContainer: {
    height: 120,
    overflow: 'hidden',
  },
  headerGradient: {
    flex: 1,
    backgroundColor: '#1B5E20',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  headerContent: {
    alignItems: 'center',
    zIndex: 2,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: '#FFFFFF',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
    marginTop: 4,
    textAlign: 'center',
  },
  headerDecoration: {
    position: 'absolute',
    top: -50,
    right: -50,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 100,
  },
  categoriesSection: {
    paddingTop: SPACING.lg,
    paddingHorizontal: SPACING.md,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1B5E20',
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  categoriesList: {
    paddingVertical: SPACING.sm,
  },
  categoryItem: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: SPACING.md,
    marginRight: SPACING.md,
    minWidth: 90,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: '#E8F5E8',
  },
  categoryIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  categoryIcon: {
    fontSize: 24,
  },
  categoryName: {
    fontSize: 12,
    color: '#1B5E20',
    textAlign: 'center',
    fontWeight: '600',
  },
  selectedCategoryName: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  searchSection: {
    paddingHorizontal: SPACING.md,
    marginTop: SPACING.lg,
  },
  searchBar: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E8F5E8',
  },
  searchIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  searchIcon: {
    fontSize: 18,
  },
  searchPlaceholder: {
    color: '#66BB6A',
    fontSize: 16,
    flex: 1,
    fontWeight: '500',
  },
  searchGlow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(46, 125, 50, 0.05)',
    borderRadius: 16,
  },
  servicesSection: {
    paddingHorizontal: SPACING.md,
    marginTop: SPACING.xl,
  },
  servicesList: {
    marginTop: SPACING.sm,
  },
  serviceCardContainer: {
    marginBottom: SPACING.md,
  },
  serviceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: SPACING.md,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E8F5E8',
  },
  serviceCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  serviceIcon: {
    fontSize: 28,
  },
  serviceTextContainer: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1B5E20',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#66BB6A',
    lineHeight: 20,
  },
  serviceArrow: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: '700',
  },
  serviceGlow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.05,
    borderRadius: 16,
  },
  alertSection: {
    paddingHorizontal: SPACING.md,
    marginTop: SPACING.xl,
  },
  alertCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: SPACING.lg,
    position: 'relative',
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: '#E8F5E8',
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  alertIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FFF3E0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  alertIcon: {
    fontSize: 24,
  },
  alertTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1B5E20',
  },
  alertText: {
    fontSize: 16,
    color: '#66BB6A',
    lineHeight: 24,
  },
  alertGlow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(46, 125, 50, 0.03)',
    borderRadius: 16,
  },
  facilitiesSection: {
    paddingHorizontal: SPACING.md,
    marginTop: SPACING.xl,
  },
  facilitiesCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: SPACING.lg,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: '#E8F5E8',
  },
  facilitiesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  facilitiesIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E8F5E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  facilitiesIcon: {
    fontSize: 28,
  },
  facilitiesTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1B5E20',
    marginBottom: 4,
  },
  facilitiesSubtitle: {
    fontSize: 14,
    color: '#66BB6A',
    lineHeight: 20,
  },
  facilitiesButton: {
    backgroundColor: '#388E3C',
    borderRadius: 12,
    paddingVertical: 14,
  },
  helpSection: {
    paddingHorizontal: SPACING.md,
    marginTop: SPACING.xl,
  },
  helpCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: SPACING.xl,
    position: 'relative',
    overflow: 'hidden',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: '#E8F5E8',
  },
  helpHeader: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  helpTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: '#1B5E20',
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  helpSubtitle: {
    fontSize: 16,
    color: '#66BB6A',
    textAlign: 'center',
    lineHeight: 24,
  },
  chatButton: {
    backgroundColor: '#2E7D32',
    borderRadius: 16,
    paddingVertical: 16,
    elevation: 4,
  },
  helpDecoration: {
    position: 'absolute',
    bottom: -30,
    right: -30,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(46, 125, 50, 0.1)',
  },
});

export default ServicesScreen;