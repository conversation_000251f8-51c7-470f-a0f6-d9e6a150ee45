import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { useMockAuth } from '../../context/MockAuthContext';
import { getDoctorAppointments, initializeMockDoctorData } from '../../data/mockDoctorData';
import AsyncStorage from '@react-native-async-storage/async-storage';

const DoctorAppointmentsScreen = ({ navigation, route }) => {
  const { currentUser } = useMockAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [activeFilter, setActiveFilter] = useState(route.params?.filter || 'all');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [notes, setNotes] = useState('');

  // Load appointments on component mount
  useEffect(() => {
    loadAppointments();
  }, [currentUser, selectedDate]);

  // Apply filters when appointments or filter changes
  useEffect(() => {
    applyFilters();
  }, [appointments, activeFilter]);

  // Load appointments for the selected date
  const loadAppointments = async () => {
    setLoading(true);
    try {
      // Ensure mock data is initialized
      await initializeMockDoctorData();

      const appointmentsData = await getDoctorAppointments(currentUser?.id, selectedDate);
      setAppointments(appointmentsData);
    } catch (error) {
      console.error('Error loading appointments:', error);
      Alert.alert('Error', 'Failed to load appointments. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Apply filters to appointments
  const applyFilters = () => {
    let filtered = [...appointments];

    switch (activeFilter) {
      case 'completed':
        filtered = filtered.filter(app => app.status === 'completed');
        break;
      case 'upcoming':
        filtered = filtered.filter(app => {
          const appTime = new Date(`2000/01/01 ${app.time}`);
          const now = new Date();
          const currentTime = new Date(`2000/01/01 ${now.getHours()}:${now.getMinutes()}`);
          return appTime > currentTime && app.status !== 'completed';
        });
        break;
      case 'all':
      default:
        // No filtering needed
        break;
    }

    // Sort by time
    filtered.sort((a, b) => {
      return new Date(`2000/01/01 ${a.time}`) - new Date(`2000/01/01 ${b.time}`);
    });

    setFilteredAppointments(filtered);
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadAppointments();
    setRefreshing(false);
  };

  // Handle date change
  const changeDate = (days) => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + days);
    setSelectedDate(newDate);
  };

  // Format date for display
  const formatDate = (date) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };

  // Handle appointment completion
  const handleCompleteAppointment = async () => {
    if (!selectedAppointment) return;

    try {
      // Get current appointments from storage
      const appointmentsJson = await AsyncStorage.getItem('@doctor_appointments');
      const allAppointments = appointmentsJson ? JSON.parse(appointmentsJson) : [];

      // Update the selected appointment
      const updatedAppointments = allAppointments.map(app =>
        app.id === selectedAppointment.id
          ? { ...app, status: 'completed', notes: notes || app.notes }
          : app
      );

      // Save back to storage
      await AsyncStorage.setItem('@doctor_appointments', JSON.stringify(updatedAppointments));

      // Update local state
      setAppointments(prevAppointments =>
        prevAppointments.map(app =>
          app.id === selectedAppointment.id
            ? { ...app, status: 'completed', notes: notes || app.notes }
            : app
        )
      );

      // Close modal and reset
      setShowCompleteModal(false);
      setSelectedAppointment(null);
      setNotes('');

      Alert.alert('Success', 'Appointment marked as completed.');
    } catch (error) {
      console.error('Error completing appointment:', error);
      Alert.alert('Error', 'Failed to complete appointment. Please try again.');
    }
  };

  // Render filter button
  const renderFilterButton = (filter, label, icon) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        activeFilter === filter && styles.activeFilterButton
      ]}
      onPress={() => setActiveFilter(filter)}
    >
      <Icon
        name={icon}
        size={18}
        color={activeFilter === filter ? COLORS.white : COLORS.darkGray}
      />
      <Text
        style={[
          styles.filterButtonText,
          activeFilter === filter && styles.activeFilterButtonText
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  // Render appointment item
  const renderAppointmentItem = ({ item }) => (
    <View style={styles.appointmentItem}>
      <View style={styles.appointmentTimeContainer}>
        <Text style={styles.appointmentTime}>{item.time}</Text>
        <Text style={styles.appointmentDuration}>{item.duration} min</Text>
      </View>
      <View style={styles.appointmentDetails}>
        <Text style={styles.patientName}>{item.patientName}</Text>
        <Text style={styles.patientInfo}>
          {item.patientAge} years • {item.patientGender}
        </Text>
        <Text style={styles.appointmentReason}>{item.reason}</Text>
        {item.notes && (
          <Text style={styles.appointmentNotes} numberOfLines={2}>
            Note: {item.notes}
          </Text>
        )}
      </View>
      <View style={styles.appointmentActions}>
        {item.status !== 'completed' ? (
          <TouchableOpacity
            style={styles.completeButton}
            onPress={() => {
              setSelectedAppointment(item);
              setNotes(item.notes || '');
              setShowCompleteModal(true);
            }}
          >
            <Icon name="check-circle" size={20} color={COLORS.white} />
            <Text style={styles.completeButtonText}>Complete</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.completedBadge}>
            <Icon name="check-circle" size={16} color={COLORS.success} />
            <Text style={styles.completedText}>Completed</Text>
          </View>
        )}
        <TouchableOpacity
          style={styles.viewButton}
          onPress={() => navigation.navigate('DoctorPatientDetails', { patientId: item.patientId })}
        >
          <Icon name="account" size={20} color={COLORS.primaryGreen} />
          <Text style={styles.viewButtonText}>View Patient</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Appointments</Text>
      </View>

      {/* Date Selector */}
      <View style={styles.dateSelector}>
        <TouchableOpacity onPress={() => changeDate(-1)}>
          <Icon name="chevron-left" size={24} color={COLORS.primaryGreen} />
        </TouchableOpacity>
        <Text style={styles.dateText}>{formatDate(selectedDate)}</Text>
        <TouchableOpacity onPress={() => changeDate(1)}>
          <Icon name="chevron-right" size={24} color={COLORS.primaryGreen} />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        {renderFilterButton('all', 'All', 'calendar-blank')}
        {renderFilterButton('upcoming', 'Upcoming', 'clock-outline')}
        {renderFilterButton('completed', 'Completed', 'check-circle')}
      </View>

      {/* Appointments List */}
      <FlatList
        data={filteredAppointments}
        renderItem={renderAppointmentItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="calendar-blank" size={64} color={COLORS.lightGray} />
            <Text style={styles.emptyText}>No appointments found</Text>
            <Text style={styles.emptySubtext}>
              {activeFilter === 'all'
                ? 'There are no appointments scheduled for this day.'
                : `There are no ${activeFilter} appointments for this day.`}
            </Text>
          </View>
        }
      />

      {/* Complete Appointment Modal */}
      <Modal
        visible={showCompleteModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCompleteModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Complete Appointment</Text>
              <TouchableOpacity onPress={() => setShowCompleteModal(false)}>
                <Icon name="close" size={24} color={COLORS.black} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <Text style={styles.modalLabel}>Patient:</Text>
              <Text style={styles.modalPatientName}>{selectedAppointment?.patientName}</Text>

              <Text style={styles.modalLabel}>Reason:</Text>
              <Text style={styles.modalText}>{selectedAppointment?.reason}</Text>

              <Text style={styles.modalLabel}>Notes:</Text>
              <TextInput
                style={styles.notesInput}
                value={notes}
                onChangeText={setNotes}
                placeholder="Add notes about the appointment"
                multiline={true}
                numberOfLines={4}
              />

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowCompleteModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={handleCompleteAppointment}
                >
                  <Text style={styles.confirmButtonText}>Complete</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    backgroundColor: COLORS.offWhite,
  },
  dateText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  filtersContainer: {
    flexDirection: 'row',
    padding: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    marginRight: SPACING.sm,
    borderRadius: 20,
    backgroundColor: COLORS.offWhite,
  },
  activeFilterButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  filterButtonText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: SPACING.xs,
  },
  activeFilterButtonText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  listContent: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra space at bottom
  },
  appointmentItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  appointmentTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  appointmentTime: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  appointmentDuration: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    backgroundColor: COLORS.offWhite,
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: 10,
  },
  appointmentDetails: {
    marginBottom: SPACING.md,
  },
  patientName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  patientInfo: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  appointmentReason: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  appointmentNotes: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  appointmentActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    paddingTop: SPACING.sm,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.success,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 5,
  },
  completeButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: 4,
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completedText: {
    color: COLORS.success,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: 4,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.offWhite,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 5,
  },
  viewButtonText: {
    color: COLORS.primaryGreen,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
    marginTop: SPACING.xl,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SPACING.md,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  modalTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  modalContent: {
    marginBottom: SPACING.md,
  },
  modalLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  modalPatientName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  modalText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
    padding: SPACING.sm,
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    textAlignVertical: 'top',
    marginBottom: SPACING.md,
    minHeight: 100,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
    padding: SPACING.md,
    borderRadius: 5,
    marginRight: SPACING.sm,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: COLORS.success,
    padding: SPACING.md,
    borderRadius: 5,
    marginLeft: SPACING.sm,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.white,
  },
});

export default DoctorAppointmentsScreen;
