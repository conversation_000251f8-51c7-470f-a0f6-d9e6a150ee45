import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { useMockAuth } from '../../context/MockAuthContext';
import mockCallData from '../../data/mockCallData';

const AdminIssueDetailsScreen = ({ navigation, route }) => {
  const { issueId } = route.params;
  const { currentUser } = useMockAuth();
  const [issue, setIssue] = useState(null);
  const [loading, setLoading] = useState(true);
  const [resolution, setResolution] = useState('');
  const [escalationReason, setEscalationReason] = useState('');
  const [escalationModalVisible, setEscalationModalVisible] = useState(false);
  const [resolveModalVisible, setResolveModalVisible] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [selectedServiceProvider, setSelectedServiceProvider] = useState('');
  const [availableServiceProviders, setAvailableServiceProviders] = useState([]);
  const [serviceProviderNotes, setServiceProviderNotes] = useState('');

  // Load issue data on mount
  useEffect(() => {
    loadIssueData();
  }, [issueId]);

  // Load issue data
  const loadIssueData = async () => {
    try {
      setLoading(true);

      // Get active issues
      const activeIssues = await mockCallData.getActiveIssues();
      let foundIssue = activeIssues.find(i => i.id === issueId);

      if (!foundIssue) {
        // Check resolved issues
        const resolvedIssues = await mockCallData.getResolvedIssues();
        foundIssue = resolvedIssues.find(i => i.id === issueId);
      }

      if (foundIssue) {
        setIssue(foundIssue);
        setResolution(foundIssue.resolution || '');
        setEscalationReason(foundIssue.escalationReason || '');
        setServiceProviderNotes(foundIssue.serviceProviderNotes || '');

        // Get available service providers based on issue category
        const category = mockCallData.CALL_CATEGORIES.find(c => c.id === foundIssue.category);
        if (category && category.serviceProviders) {
          console.log('Available service providers:', category.serviceProviders);
          setAvailableServiceProviders(category.serviceProviders);

          // If issue already has an assigned service provider, select it
          if (foundIssue.assignedServiceProvider) {
            setSelectedServiceProvider(foundIssue.assignedServiceProvider);
          } else if (category.serviceProviders.length > 0) {
            // Default select the first service provider if none is assigned
            setSelectedServiceProvider(category.serviceProviders[0]);
          }
        } else {
          console.warn('No service providers found for category:', foundIssue.category);
          // Set default service providers if category not found
          setAvailableServiceProviders([
            'General Support',
            'Department Coordinator',
            'Service Manager',
            'Municipal Services',
            'Community Services Officer'
          ]);
        }
      } else {
        Alert.alert('Error', 'Issue not found');
        navigation.goBack();
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading issue data:', error);
      setLoading(false);
      Alert.alert('Error', 'Failed to load issue data');
    }
  };

  // Handle escalate issue
  const handleEscalateIssue = async () => {
    if (!escalationReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for escalation');
      return;
    }

    try {
      const updatedIssue = await mockCallData.updateIssue(issueId, {
        escalated: true,
        escalationReason,
        escalatedBy: currentUser.name,
        escalatedAt: new Date().toISOString(),
        status: 'escalated'
      });

      setIssue(updatedIssue);
      setEscalationModalVisible(false);

      Alert.alert('Success', 'Issue has been escalated');
    } catch (error) {
      console.error('Error escalating issue:', error);
      Alert.alert('Error', 'Failed to escalate issue');
    }
  };

  // Handle resolve issue
  const handleResolveIssue = async () => {
    if (!resolution.trim()) {
      Alert.alert('Error', 'Please provide a resolution');
      return;
    }

    try {
      const resolvedAt = new Date().toISOString();
      const resolutionTime = calculateResolutionTime(issue.timestamp, resolvedAt);

      const updatedIssue = await mockCallData.updateIssue(issueId, {
        status: 'resolved',
        resolution,
        resolvedBy: currentUser.name,
        resolvedAt,
        resolutionTime, // Store the calculated resolution time
      });

      setIssue(updatedIssue);
      setResolveModalVisible(false);

      Alert.alert('Success', `Issue has been resolved in ${resolutionTime}`);
    } catch (error) {
      console.error('Error resolving issue:', error);
      Alert.alert('Error', 'Failed to resolve issue');
    }
  };

  // Handle assign service provider
  const handleAssignServiceProvider = async () => {
    if (!selectedServiceProvider) {
      Alert.alert('Error', 'Please select a service provider');
      return;
    }

    try {
      const updatedIssue = await mockCallData.updateIssue(issueId, {
        assignedServiceProvider: selectedServiceProvider,
        assignedAt: new Date().toISOString(),
        assignedBy: currentUser.name,
        serviceProviderNotes,
        status: issue.status === 'new' ? 'in-progress' : issue.status,
      });

      setIssue(updatedIssue);
      setAssignModalVisible(false);

      Alert.alert('Success', `Issue has been assigned to ${selectedServiceProvider}`);
    } catch (error) {
      console.error('Error assigning service provider:', error);
      Alert.alert('Error', 'Failed to assign service provider');
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Calculate resolution time
  const calculateResolutionTime = (createdAt, resolvedAt) => {
    if (!createdAt || !resolvedAt) return 'N/A';

    const created = new Date(createdAt);
    const resolved = new Date(resolvedAt);

    // Calculate time difference in milliseconds
    const timeDiff = resolved - created;

    // Convert to appropriate units
    const minutes = Math.floor(timeDiff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ${hours % 24} hour${hours % 24 !== 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes % 60} minute${minutes % 60 !== 1 ? 's' : ''}`;
    } else {
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    const priorityLevel = mockCallData.PRIORITY_LEVELS.find(p => p.id === priority);
    return priorityLevel ? priorityLevel.color : COLORS.darkGray;
  };

  // Get priority name
  const getPriorityName = (priority) => {
    const priorityLevel = mockCallData.PRIORITY_LEVELS.find(p => p.id === priority);
    return priorityLevel ? priorityLevel.name : 'Unknown';
  };

  // Get status color
  const getStatusColor = (status, escalated) => {
    if (escalated) return COLORS.error;

    switch (status) {
      case 'new':
        return COLORS.info;
      case 'in-progress':
        return COLORS.primaryGreen;
      case 'resolved':
        return COLORS.success;
      default:
        return COLORS.darkGray;
    }
  };

  // Get status name
  const getStatusName = (status, escalated) => {
    if (escalated) return 'Escalated';

    switch (status) {
      case 'new':
        return 'New';
      case 'in-progress':
        return 'In Progress';
      case 'resolved':
        return 'Resolved';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading issue data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Issue Details</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
        {/* Issue Header */}
        <View style={styles.issueHeader}>
          <View style={styles.referenceContainer}>
            <Text style={styles.referenceLabel}>Reference Number:</Text>
            <Text style={styles.referenceNumber}>{issue.referenceNumber}</Text>
          </View>

          <View style={styles.statusContainer}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: getStatusColor(issue.status, issue.escalated) }
            ]}>
              <Text style={styles.statusText}>
                {getStatusName(issue.status, issue.escalated)}
              </Text>
            </View>

            <View style={[
              styles.priorityIndicator,
              { backgroundColor: getPriorityColor(issue.priority) }
            ]}>
              <Text style={styles.priorityText}>
                {getPriorityName(issue.priority)}
              </Text>
            </View>
          </View>
        </View>

        {/* Issue Details */}
        <View style={styles.detailsCard}>
          <Text style={styles.cardTitle}>Issue Details</Text>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailValue}>
              {mockCallData.CALL_CATEGORIES.find(c => c.id === issue.category)?.name || issue.category}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Created:</Text>
            <Text style={styles.detailValue}>{formatDate(issue.timestamp)}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Handled By:</Text>
            <Text style={styles.detailValue}>{issue.handledBy}</Text>
          </View>

          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionLabel}>Description:</Text>
            <Text style={styles.descriptionText}>{issue.description}</Text>
          </View>

          {issue.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Agent Notes:</Text>
              <Text style={styles.notesText}>{issue.notes}</Text>
            </View>
          )}

          {issue.escalated && issue.escalationReason && (
            <View style={styles.escalationContainer}>
              <Text style={styles.escalationLabel}>Escalation Reason:</Text>
              <Text style={styles.escalationText}>{issue.escalationReason}</Text>
              <Text style={styles.escalationInfo}>
                Escalated by {issue.escalatedBy} on {formatDate(issue.escalatedAt)}
              </Text>
            </View>
          )}

          {issue.status === 'resolved' && issue.resolution && (
            <View style={styles.resolutionContainer}>
              <Text style={styles.resolutionLabel}>Resolution:</Text>
              <Text style={styles.resolutionText}>{issue.resolution}</Text>
              <Text style={styles.resolutionInfo}>
                Resolved by {issue.resolvedBy} on {formatDate(issue.resolvedAt)}
              </Text>
              <View style={styles.resolutionTimeContainer}>
                <Icon name="clock-outline" size={16} color={COLORS.success} style={styles.resolutionTimeIcon} />
                <Text style={styles.resolutionTimeText}>
                  Time to resolve: {calculateResolutionTime(issue.timestamp, issue.resolvedAt)}
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Caller Information */}
        <View style={styles.detailsCard}>
          <Text style={styles.cardTitle}>Caller Information</Text>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Name:</Text>
            <Text style={styles.detailValue}>{issue.userName}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>ID Number:</Text>
            <Text style={styles.detailValue}>{issue.userIdNumber || 'Not provided'}</Text>
          </View>
        </View>

        {/* Answers to Standard Questions */}
        {issue.answers && Object.keys(issue.answers).length > 0 && (
          <View style={styles.detailsCard}>
            <Text style={styles.cardTitle}>Call Information</Text>

            {Object.entries(issue.answers).map(([questionId, answer]) => {
              const question = mockCallData.STANDARD_QUESTIONS.find(q => q.id === questionId);
              if (!question) return null;

              return (
                <View key={questionId} style={styles.detailItem}>
                  <Text style={styles.detailLabel}>{question.question}</Text>
                  <Text style={styles.detailValue}>{answer}</Text>
                </View>
              );
            })}
          </View>
        )}

        {/* Service Provider Information */}
        {issue.assignedServiceProvider ? (
          <View style={styles.detailsCard}>
            <Text style={styles.cardTitle}>Service Provider</Text>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Assigned To:</Text>
              <Text style={styles.detailValue}>{issue.assignedServiceProvider}</Text>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Assigned By:</Text>
              <Text style={styles.detailValue}>{issue.assignedBy || currentUser.name}</Text>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Assigned On:</Text>
              <Text style={styles.detailValue}>{formatDate(issue.assignedAt)}</Text>
            </View>

            {issue.serviceProviderNotes && (
              <View style={styles.notesContainer}>
                <Text style={styles.notesLabel}>Service Provider Notes:</Text>
                <Text style={styles.notesText}>{issue.serviceProviderNotes}</Text>
              </View>
            )}

            {issue.status !== 'resolved' && (
              <TouchableOpacity
                style={styles.reassignButton}
                onPress={() => {
                  setSelectedServiceProvider(issue.assignedServiceProvider);
                  setServiceProviderNotes(issue.serviceProviderNotes || '');
                  setAssignModalVisible(true);
                }}
              >
                <Icon name="account-switch" size={20} color={COLORS.white} />
                <Text style={styles.reassignButtonText}>Reassign Service Provider</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : issue.status !== 'resolved' && (
          <View style={styles.detailsCard}>
            <Text style={styles.cardTitle}>Service Provider</Text>
            <Text style={styles.noProviderText}>No service provider assigned yet</Text>

            <TouchableOpacity
              style={styles.assignButton}
              onPress={() => setAssignModalVisible(true)}
            >
              <Icon name="account-plus" size={20} color={COLORS.white} />
              <Text style={styles.assignButtonText}>Assign Service Provider</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Action Buttons */}
        {issue.status !== 'resolved' && (
          <View style={styles.actionButtons}>
            {!issue.escalated && (
              <TouchableOpacity
                style={[styles.actionButton, styles.escalateButton]}
                onPress={() => setEscalationModalVisible(true)}
              >
                <Icon name="arrow-up-bold-circle" size={20} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Escalate Issue</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.actionButton, styles.resolveButton]}
              onPress={() => setResolveModalVisible(true)}
            >
              <Icon name="check-circle" size={20} color={COLORS.white} />
              <Text style={styles.actionButtonText}>Resolve Issue</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Escalation Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={escalationModalVisible}
        onRequestClose={() => setEscalationModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Escalate Issue</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setEscalationModalVisible(false)}
              >
                <Icon name="close" size={24} color={COLORS.darkGray} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.modalLabel}>Reason for Escalation:</Text>
              <TextInput
                style={styles.modalTextarea}
                value={escalationReason}
                onChangeText={setEscalationReason}
                placeholder="Provide a reason for escalating this issue"
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />

              <TouchableOpacity
                style={styles.modalButton}
                onPress={handleEscalateIssue}
              >
                <Icon name="arrow-up-bold-circle" size={20} color={COLORS.white} />
                <Text style={styles.modalButtonText}>Escalate Issue</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Resolve Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={resolveModalVisible}
        onRequestClose={() => setResolveModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Resolve Issue</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setResolveModalVisible(false)}
              >
                <Icon name="close" size={24} color={COLORS.darkGray} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.modalLabel}>Resolution:</Text>
              <TextInput
                style={styles.modalTextarea}
                value={resolution}
                onChangeText={setResolution}
                placeholder="Describe how this issue was resolved"
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />

              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: COLORS.success }]}
                onPress={handleResolveIssue}
              >
                <Icon name="check-circle" size={20} color={COLORS.white} />
                <Text style={styles.modalButtonText}>Resolve Issue</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Service Provider Assignment Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={assignModalVisible}
        onRequestClose={() => setAssignModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {issue?.assignedServiceProvider ? 'Reassign Service Provider' : 'Assign Service Provider'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setAssignModalVisible(false)}
              >
                <Icon name="close" size={24} color={COLORS.darkGray} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.modalLabel}>Select Service Provider:</Text>

              <View style={styles.providerListContainer}>
                <Text style={styles.providerListHeader}>
                  Available Service Providers ({availableServiceProviders.length})
                </Text>
                <FlatList
                  data={availableServiceProviders}
                  keyExtractor={(item) => item}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={[
                        styles.providerOption,
                        selectedServiceProvider === item && styles.selectedProviderOption
                      ]}
                      onPress={() => setSelectedServiceProvider(item)}
                    >
                      <View style={styles.providerOptionContent}>
                        <Icon
                          name={selectedServiceProvider === item ? "checkbox-marked-circle" : "checkbox-blank-circle-outline"}
                          size={24}
                          color={selectedServiceProvider === item ? COLORS.primaryGreen : COLORS.darkGray}
                        />
                        <Text style={[
                          styles.providerOptionText,
                          selectedServiceProvider === item && styles.selectedProviderOptionText
                        ]}>
                          {item}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  )}
                  style={styles.providerList}
                  showsVerticalScrollIndicator={true}
                  initialNumToRender={8}
                />
              </View>

              <Text style={styles.modalLabel}>Notes for Service Provider:</Text>
              <TextInput
                style={styles.modalTextarea}
                value={serviceProviderNotes}
                onChangeText={setServiceProviderNotes}
                placeholder="Add any notes or instructions for the service provider"
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />

              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: COLORS.primaryGreen }]}
                onPress={handleAssignServiceProvider}
              >
                <Icon
                  name={issue?.assignedServiceProvider ? "account-switch" : "account-plus"}
                  size={20}
                  color={COLORS.white}
                />
                <Text style={styles.modalButtonText}>
                  {issue?.assignedServiceProvider ? 'Reassign Service Provider' : 'Assign Service Provider'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.darkGray,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.md,
    paddingBottom: SPACING.xxl,
  },
  issueHeader: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  referenceContainer: {
    marginBottom: SPACING.sm,
  },
  referenceLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  referenceNumber: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: SPACING.sm,
  },
  statusText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
  },
  priorityIndicator: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
  },
  detailsCard: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    paddingBottom: SPACING.xs,
  },
  detailItem: {
    marginBottom: SPACING.sm,
  },
  detailLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  detailValue: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    fontWeight: FONT_WEIGHTS.medium,
  },
  descriptionContainer: {
    marginTop: SPACING.md,
    padding: SPACING.sm,
    backgroundColor: COLORS.offWhite,
    borderRadius: 5,
  },
  descriptionLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  descriptionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    lineHeight: 22,
  },
  notesContainer: {
    marginTop: SPACING.md,
    padding: SPACING.sm,
    backgroundColor: '#F5F5F5',
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.primaryGreen,
  },
  notesLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  notesText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    fontStyle: 'italic',
    lineHeight: 22,
  },
  escalationContainer: {
    marginTop: SPACING.md,
    padding: SPACING.sm,
    backgroundColor: '#FFEBEE',
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.error,
  },
  escalationLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  escalationText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    lineHeight: 22,
    marginBottom: SPACING.xs,
  },
  escalationInfo: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  resolutionContainer: {
    marginTop: SPACING.md,
    padding: SPACING.sm,
    backgroundColor: '#E8F5E9',
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.success,
  },
  resolutionLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  resolutionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    lineHeight: 22,
    marginBottom: SPACING.xs,
  },
  resolutionInfo: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  resolutionTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.sm,
    backgroundColor: COLORS.success + '15',
    padding: SPACING.xs,
    borderRadius: 4,
  },
  resolutionTimeIcon: {
    marginRight: SPACING.xs,
  },
  resolutionTimeText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.success,
    fontWeight: FONT_WEIGHTS.medium,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 5,
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  escalateButton: {
    backgroundColor: COLORS.error,
  },
  resolveButton: {
    backgroundColor: COLORS.success,
  },
  actionButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    marginLeft: SPACING.xs,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.primaryGreen,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  modalTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  modalBody: {
    padding: SPACING.lg,
  },
  modalLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  modalTextarea: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
    padding: SPACING.md,
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    backgroundColor: COLORS.white,
    height: 120,
    textAlignVertical: 'top',
    marginBottom: SPACING.lg,
  },
  modalButton: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 5,
  },
  modalButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    marginLeft: SPACING.sm,
  },
  assignButton: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 5,
    marginTop: SPACING.md,
  },
  assignButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    marginLeft: SPACING.sm,
  },
  reassignButton: {
    backgroundColor: COLORS.info,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 5,
    marginTop: SPACING.md,
  },
  reassignButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    marginLeft: SPACING.sm,
  },
  noProviderText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    fontStyle: 'italic',
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  providerListContainer: {
    marginBottom: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
  },
  providerListHeader: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.sm,
    textAlign: 'center',
  },
  providerList: {
    height: 250,
  },
  providerOption: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  selectedProviderOption: {
    backgroundColor: '#E8F5E9',
  },
  providerOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  providerOptionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    marginLeft: SPACING.sm,
  },
  selectedProviderOptionText: {
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.primaryGreen,
  },
});

export default AdminIssueDetailsScreen;
