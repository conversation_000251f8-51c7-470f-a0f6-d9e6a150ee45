import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import mockCallData from '../../data/mockCallData';

const AdminIssuesScreen = ({ navigation, route }) => {
  const initialFilter = route.params?.filter || 'all';
  const [activeIssues, setActiveIssues] = useState([]);
  const [resolvedIssues, setResolvedIssues] = useState([]);
  const [displayedIssues, setDisplayedIssues] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState(initialFilter);

  // Load issues on mount
  useEffect(() => {
    loadIssues();
  }, []);

  // Update displayed issues when filter changes
  useEffect(() => {
    updateDisplayedIssues();
  }, [filter, activeIssues, resolvedIssues]);

  // Load issues
  const loadIssues = async () => {
    try {
      setRefreshing(true);

      // Get active issues
      const active = await mockCallData.getActiveIssues();
      setActiveIssues(active);

      // Get resolved issues
      const resolved = await mockCallData.getResolvedIssues();
      setResolvedIssues(resolved);

      setRefreshing(false);
    } catch (error) {
      console.error('Error loading issues:', error);
      setRefreshing(false);
      Alert.alert('Error', 'Failed to load issues');
    }
  };

  // Update displayed issues based on filter
  const updateDisplayedIssues = () => {
    switch (filter) {
      case 'active':
        setDisplayedIssues(activeIssues);
        break;
      case 'resolved':
        setDisplayedIssues(resolvedIssues);
        break;
      case 'escalated':
        setDisplayedIssues(activeIssues.filter(issue => issue.escalated));
        break;
      case 'unassigned':
        setDisplayedIssues(activeIssues.filter(issue => !issue.assignedServiceProvider));
        break;
      case 'assigned':
        setDisplayedIssues([
          ...activeIssues.filter(issue => issue.assignedServiceProvider),
          ...resolvedIssues.filter(issue => issue.assignedServiceProvider)
        ]);
        break;
      case 'all':
      default:
        setDisplayedIssues([...activeIssues, ...resolvedIssues]);
        break;
    }
  };

  // Handle refresh
  const onRefresh = () => {
    loadIssues();
  };

  // Get status color
  const getStatusColor = (status, escalated) => {
    if (escalated) return COLORS.error;

    switch (status) {
      case 'new':
        return COLORS.info;
      case 'in-progress':
        return COLORS.primaryGreen;
      case 'resolved':
        return COLORS.success;
      default:
        return COLORS.darkGray;
    }
  };

  // Get status icon
  const getStatusIcon = (status, escalated) => {
    if (escalated) return 'alert-circle';

    switch (status) {
      case 'new':
        return 'file-document-outline';
      case 'in-progress':
        return 'progress-clock';
      case 'resolved':
        return 'check-circle';
      default:
        return 'information-outline';
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Render issue item
  const renderIssueItem = ({ item }) => {
    // Get category name from category ID
    const category = mockCallData.CALL_CATEGORIES.find(c => c.id === item.category);
    const categoryName = category ? category.name : item.category;

    return (
      <TouchableOpacity
        style={styles.issueItem}
        onPress={() => navigation.navigate('AdminIssueDetails', { issueId: item.id })}
      >
        <View style={styles.issueHeader}>
          <View style={styles.issueInfo}>
            <Text style={styles.issueCategory}>{categoryName}</Text>
            <Text style={styles.issueDate}>{formatDate(item.timestamp)}</Text>
          </View>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: getStatusColor(item.status, item.escalated) + '20' }
          ]}>
            <Icon
              name={getStatusIcon(item.status, item.escalated)}
              size={16}
              color={getStatusColor(item.status, item.escalated)}
            />
            <Text style={[
              styles.statusText,
              { color: getStatusColor(item.status, item.escalated) }
            ]}>
              {item.escalated ? 'Escalated' :
               item.status === 'in-progress' ? 'In Progress' :
               item.status === 'resolved' ? 'Resolved' : 'New'}
            </Text>
          </View>
        </View>

        <View style={styles.issueBody}>
          <Text style={styles.issueDescription} numberOfLines={2}>
            {item.description}
          </Text>
        </View>

        {item.assignedServiceProvider && (
          <View style={styles.serviceProviderContainer}>
            <Icon name="account-hard-hat" size={16} color={COLORS.primaryGreen} />
            <Text style={styles.serviceProviderText}>
              Assigned to: <Text style={styles.serviceProviderName}>{item.assignedServiceProvider}</Text>
            </Text>
          </View>
        )}

        <View style={styles.issueFooter}>
          <View style={styles.referenceContainer}>
            <Text style={styles.referenceLabel}>Ref:</Text>
            <Text style={styles.referenceNumber}>{item.referenceNumber}</Text>
          </View>
          <View style={styles.handledByContainer}>
            <Text style={styles.handledByLabel}>Handled by:</Text>
            <Text style={styles.handledByName}>{item.handledBy}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Issues</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
        >
          <Icon name="refresh" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterTabsContainer}
      >
        <View style={styles.filterTabs}>
          <TouchableOpacity
            style={[styles.filterTab, filter === 'all' && styles.activeFilterTab]}
            onPress={() => setFilter('all')}
          >
            <Text style={[styles.filterTabText, filter === 'all' && styles.activeFilterTabText]}>
              All
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, filter === 'active' && styles.activeFilterTab]}
            onPress={() => setFilter('active')}
          >
            <Text style={[styles.filterTabText, filter === 'active' && styles.activeFilterTabText]}>
              Active
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, filter === 'resolved' && styles.activeFilterTab]}
            onPress={() => setFilter('resolved')}
          >
            <Text style={[styles.filterTabText, filter === 'resolved' && styles.activeFilterTabText]}>
              Resolved
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, filter === 'escalated' && styles.activeFilterTab]}
            onPress={() => setFilter('escalated')}
          >
            <Text style={[styles.filterTabText, filter === 'escalated' && styles.activeFilterTabText]}>
              Escalated
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, filter === 'unassigned' && styles.activeFilterTab]}
            onPress={() => setFilter('unassigned')}
          >
            <Text style={[styles.filterTabText, filter === 'unassigned' && styles.activeFilterTabText]}>
              Unassigned
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, filter === 'assigned' && styles.activeFilterTab]}
            onPress={() => setFilter('assigned')}
          >
            <Text style={[styles.filterTabText, filter === 'assigned' && styles.activeFilterTabText]}>
              Assigned
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Issues List */}
      <FlatList
        data={displayedIssues}
        renderItem={renderIssueItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="clipboard-text-off" size={60} color={COLORS.lightGray} />
            <Text style={styles.emptyText}>No issues found</Text>
            <Text style={styles.emptySubtext}>
              {filter === 'all' ? 'There are no issues to display' :
               filter === 'active' ? 'There are no active issues' :
               filter === 'resolved' ? 'There are no resolved issues' :
               'There are no escalated issues'}
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  refreshButton: {
    padding: SPACING.xs,
  },
  filterTabsContainer: {
    backgroundColor: COLORS.white,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  filterTabs: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
  },
  filterTab: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    minWidth: 100,
  },
  activeFilterTab: {
    borderBottomColor: COLORS.primaryGreen,
  },
  filterTabText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
  },
  activeFilterTabText: {
    color: COLORS.primaryGreen,
  },
  listContainer: {
    padding: SPACING.md,
    paddingBottom: SPACING.xxl,
  },
  issueItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  issueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  issueInfo: {
    flex: 1,
  },
  issueCategory: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  issueDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: 4,
  },
  issueBody: {
    marginBottom: SPACING.sm,
    paddingBottom: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  issueDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  serviceProviderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    paddingBottom: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  serviceProviderText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: SPACING.xs,
  },
  serviceProviderName: {
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.primaryGreen,
  },
  issueFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  referenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  referenceLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginRight: 4,
  },
  referenceNumber: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  handledByContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  handledByLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginRight: 4,
  },
  handledByName: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginTop: SPACING.sm,
    paddingHorizontal: SPACING.xl,
  },
});

export default AdminIssuesScreen;
