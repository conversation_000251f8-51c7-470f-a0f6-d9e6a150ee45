import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

const PaymentDetailsScreen = ({ navigation, route }) => {
  // Get bill details from route params
  const { bill } = route.params || {};
  
  // Form state
  const [cardNumber, setCardNumber] = useState('');
  const [cardName, setCardName] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [saveCard, setSaveCard] = useState(false);
  
  // Form validation
  const [errors, setErrors] = useState({});
  
  // Format card number with spaces
  const formatCardNumber = (text) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    // Add space after every 4 digits
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
    // Limit to 19 characters (16 digits + 3 spaces)
    return formatted.slice(0, 19);
  };
  
  // Format expiry date (MM/YY)
  const formatExpiryDate = (text) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    // Add slash after first 2 digits
    if (cleaned.length > 2) {
      return cleaned.slice(0, 2) + '/' + cleaned.slice(2, 4);
    }
    return cleaned;
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!cardNumber || cardNumber.replace(/\s/g, '').length !== 16) {
      newErrors.cardNumber = 'Please enter a valid 16-digit card number';
    }
    
    if (!cardName) {
      newErrors.cardName = 'Please enter the name on card';
    }
    
    if (!expiryDate || expiryDate.length !== 5) {
      newErrors.expiryDate = 'Please enter a valid expiry date (MM/YY)';
    } else {
      const [month, year] = expiryDate.split('/');
      const currentYear = new Date().getFullYear() % 100;
      const currentMonth = new Date().getMonth() + 1;
      
      if (parseInt(month) < 1 || parseInt(month) > 12) {
        newErrors.expiryDate = 'Invalid month';
      } else if (parseInt(year) < currentYear || 
                (parseInt(year) === currentYear && parseInt(month) < currentMonth)) {
        newErrors.expiryDate = 'Card has expired';
      }
    }
    
    if (!cvv || cvv.length !== 3) {
      newErrors.cvv = 'Please enter a valid 3-digit CVV';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle payment submission
  const handlePayment = () => {
    if (validateForm()) {
      // Generate a random reference number
      const refNumber = 'PAY-' + Math.random().toString(36).substring(2, 8).toUpperCase();
      
      // Navigate to confirmation screen
      navigation.navigate('PaymentConfirmation', {
        bill,
        refNumber,
        paymentDate: new Date().toISOString(),
      });
    } else {
      // Scroll to the top to show errors
      Alert.alert('Validation Error', 'Please check the form for errors.');
    }
  };
  
  // Format currency
  const formatCurrency = (amount) => {
    return `R ${amount.toFixed(2)}`;
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Details</Text>
        <View style={styles.rightPlaceholder} />
      </View>
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Bill Summary */}
          <View style={styles.billSummary}>
            <View style={styles.billHeader}>
              <Icon name={getBillIcon(bill?.type)} size={24} color={COLORS.primaryGreen} />
              <Text style={styles.billTitle}>{bill?.type} Bill Payment</Text>
            </View>
            
            <View style={styles.billDetails}>
              <View style={styles.billRow}>
                <Text style={styles.billLabel}>Account Number:</Text>
                <Text style={styles.billValue}>{bill?.accountNumber}</Text>
              </View>
              
              <View style={styles.billRow}>
                <Text style={styles.billLabel}>Amount Due:</Text>
                <Text style={styles.billAmount}>{formatCurrency(bill?.amount || 0)}</Text>
              </View>
              
              <View style={styles.billRow}>
                <Text style={styles.billLabel}>Due Date:</Text>
                <Text style={styles.billValue}>
                  {formatDate(bill?.dueDate)}
                </Text>
              </View>
            </View>
          </View>
          
          {/* Payment Method */}
          <View style={styles.paymentMethodSection}>
            <Text style={styles.sectionTitle}>Payment Method</Text>
            
            <View style={styles.cardInputContainer}>
              <Text style={styles.inputLabel}>Card Number</Text>
              <View style={styles.cardNumberContainer}>
                <Icon name="credit-card-outline" size={24} color={COLORS.darkGray} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="1234 5678 9012 3456"
                  value={cardNumber}
                  onChangeText={(text) => setCardNumber(formatCardNumber(text))}
                  keyboardType="numeric"
                  maxLength={19}
                />
              </View>
              {errors.cardNumber && <Text style={styles.errorText}>{errors.cardNumber}</Text>}
              
              <Text style={styles.inputLabel}>Name on Card</Text>
              <TextInput
                style={styles.input}
                placeholder="John Doe"
                value={cardName}
                onChangeText={setCardName}
              />
              {errors.cardName && <Text style={styles.errorText}>{errors.cardName}</Text>}
              
              <View style={styles.cardDetailsRow}>
                <View style={styles.expiryContainer}>
                  <Text style={styles.inputLabel}>Expiry Date</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="MM/YY"
                    value={expiryDate}
                    onChangeText={(text) => setExpiryDate(formatExpiryDate(text))}
                    keyboardType="numeric"
                    maxLength={5}
                  />
                  {errors.expiryDate && <Text style={styles.errorText}>{errors.expiryDate}</Text>}
                </View>
                
                <View style={styles.cvvContainer}>
                  <Text style={styles.inputLabel}>CVV</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="123"
                    value={cvv}
                    onChangeText={(text) => setCvv(text.replace(/\D/g, '').slice(0, 3))}
                    keyboardType="numeric"
                    maxLength={3}
                    secureTextEntry
                  />
                  {errors.cvv && <Text style={styles.errorText}>{errors.cvv}</Text>}
                </View>
              </View>
              
              <TouchableOpacity 
                style={styles.saveCardContainer}
                onPress={() => setSaveCard(!saveCard)}
              >
                <View style={styles.checkbox}>
                  {saveCard && <Icon name="check" size={16} color={COLORS.primaryGreen} />}
                </View>
                <Text style={styles.saveCardText}>Save card for future payments</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Payment Summary */}
          <View style={styles.paymentSummary}>
            <Text style={styles.sectionTitle}>Payment Summary</Text>
            
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>{bill?.type} Bill</Text>
              <Text style={styles.summaryValue}>{formatCurrency(bill?.amount || 0)}</Text>
            </View>
            
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Service Fee</Text>
              <Text style={styles.summaryValue}>R 0.00</Text>
            </View>
            
            <View style={styles.divider} />
            
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>{formatCurrency(bill?.amount || 0)}</Text>
            </View>
          </View>
          
          {/* Pay Button */}
          <CustomButton
            title={`Pay ${formatCurrency(bill?.amount || 0)}`}
            onPress={handlePayment}
            style={styles.payButton}
            icon={<Icon name="check-circle-outline" size={20} color={COLORS.white} />}
          />
          
          {/* Security Note */}
          <View style={styles.securityNote}>
            <Icon name="shield-check-outline" size={20} color={COLORS.darkGray} />
            <Text style={styles.securityText}>
              Your payment information is secure and encrypted.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// Helper function to get icon based on bill type
const getBillIcon = (type) => {
  switch (type) {
    case 'Electricity':
      return 'flash';
    case 'Water':
      return 'water';
    case 'Property Tax':
      return 'home-city';
    case 'Waste Management':
      return 'delete';
    default:
      return 'receipt';
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  rightPlaceholder: {
    width: 24,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra padding for bottom tab bar
  },
  billSummary: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  billHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  billTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginLeft: SPACING.sm,
  },
  billDetails: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
  },
  billRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  billLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  billValue: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  billAmount: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  paymentMethodSection: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
  },
  cardInputContainer: {
    width: '100%',
  },
  inputLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  cardNumberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 8,
    paddingHorizontal: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  inputIcon: {
    marginRight: SPACING.sm,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 8,
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
  },
  cardDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  expiryContainer: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  cvvContainer: {
    flex: 1,
  },
  errorText: {
    color: COLORS.error,
    fontSize: FONT_SIZES.xs,
    marginTop: -SPACING.xs,
    marginBottom: SPACING.sm,
  },
  saveCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.sm,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
    borderRadius: 4,
    marginRight: SPACING.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveCardText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  paymentSummary: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  summaryValue: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.lightGray,
    marginVertical: SPACING.sm,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.xs,
  },
  totalLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  totalValue: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  payButton: {
    marginBottom: SPACING.md,
  },
  securityNote: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.xl,
  },
  securityText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: SPACING.xs,
  },
});

export default PaymentDetailsScreen;
