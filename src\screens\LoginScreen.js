import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  Alert,
} from 'react-native';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomInput from '../components/CustomInput';
import CustomButton from '../components/CustomButton';
import { useMockAuth } from '../context/MockAuthContext';
import { IMAGES } from '../assets/images';

const { width, height } = Dimensions.get('window');

const LoginScreen = ({ navigation }) => {
  const [idNumber, setIdNumber] = useState('');
  const [password, setPassword] = useState('');
  const [idNumberError, setIdNumberError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  const { signIn, loading, authError, clearError, needsConfirmation, tempIdNumber } = useMockAuth();

  // Check if user needs to confirm their account
  useEffect(() => {
    if (needsConfirmation && tempIdNumber) {
      navigation.navigate('Confirmation', { idNumber: tempIdNumber });
    }
  }, [needsConfirmation, tempIdNumber, navigation]);

  const validateIdNumber = (idNumber) => {
    // South African ID number is 13 digits
    const idNumberRegex = /^\d{13}$/;
    if (!idNumber) {
      setIdNumberError('ID Number is required');
      return false;
    } else if (!idNumberRegex.test(idNumber)) {
      setIdNumberError('Please enter a valid 13-digit ID Number');
      return false;
    }
    setIdNumberError('');
    return true;
  };

  const validatePassword = (password) => {
    if (!password) {
      setPasswordError('Password is required');
      return false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      return false;
    }
    setPasswordError('');
    return true;
  };

  const handleLogin = async () => {
    console.log('Login button pressed');

    const isIdNumberValid = validateIdNumber(idNumber);
    const isPasswordValid = validatePassword(password);

    if (isIdNumberValid && isPasswordValid) {
      console.log('Validation passed, attempting to sign in');

      try {
        const result = await signIn(idNumber, password);
        console.log('Sign in result:', result);

        if (result.success) {
          console.log('Login successful, user:', result.user);

          // Force navigation to the appropriate screen based on user role
          if (result.user.role === 'admin') {
            navigation.reset({
              index: 0,
              routes: [{ name: 'AdminDashboard' }],
            });
          } else if (result.user.role === 'doctor') {
            navigation.reset({
              index: 0,
              routes: [{ name: 'DoctorDashboard' }],
            });
          } else {
            navigation.reset({
              index: 0,
              routes: [{ name: 'Main' }],
            });
          }
        } else {
          console.error('Login failed:', result.error);
          Alert.alert('Login Failed', result.error || 'An error occurred during login');
        }
      } catch (error) {
        console.error('Exception during login:', error);
        Alert.alert('Login Error', 'An unexpected error occurred. Please try again.');
      }
    } else {
      console.log('Validation failed');
    }
  };

  const handleRegister = () => {
    // Navigate to register screen
    navigation.navigate('Register');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.appName}>Let's Talk</Text>
            <Text style={styles.tagline}>Connect with friends and family</Text>
          </View>

          <View style={styles.logoContainer}>
            <Image
              source={IMAGES.logo}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.welcomeText}>Welcome Back!</Text>
            <Text style={styles.loginText}>Login to your account</Text>

            {authError && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{authError}</Text>
                <TouchableOpacity onPress={clearError}>
                  <Text style={styles.dismissText}>Dismiss</Text>
                </TouchableOpacity>
              </View>
            )}

            <CustomInput
              label="ID Number"
              placeholder="Enter your 13-digit ID Number"
              value={idNumber}
              onChangeText={(text) => {
                // Only allow digits and limit to 13 characters
                const sanitizedValue = text.replace(/[^0-9]/g, '').slice(0, 13);
                setIdNumber(sanitizedValue);
                if (idNumberError) validateIdNumber(sanitizedValue);
              }}
              keyboardType="numeric"
              error={idNumberError}
              maxLength={13}
            />

            <CustomInput
              label="Password"
              placeholder="Enter your password"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                if (passwordError) validatePassword(text);
              }}
              secureTextEntry
              error={passwordError}
            />

            <TouchableOpacity style={styles.forgotPasswordContainer}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>

            <CustomButton
              title="Login"
              onPress={handleLogin}
              loading={loading}
              style={styles.loginButton}
            />

            <View style={styles.registerContainer}>
              <Text style={styles.noAccountText}>Don't have an account? </Text>
              <TouchableOpacity onPress={handleRegister}>
                <Text style={styles.registerText}>Register</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: SPACING.xl,
  },
  header: {
    alignItems: 'center',
    marginTop: SPACING.xl,
  },
  appName: {
    fontSize: FONT_SIZES.title,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.xs,
  },
  tagline: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  logoContainer: {
    marginVertical: SPACING.xl,
    alignItems: 'center',
  },
  logo: {
    width: 180,
    height: 180,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  formContainer: {
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  welcomeText: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  loginText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.xl,
    textAlign: 'center',
  },
  errorContainer: {
    width: '100%',
    backgroundColor: '#FFEBEE',
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: FONT_SIZES.sm,
    flex: 1,
  },
  dismissText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
    fontSize: FONT_SIZES.sm,
  },
  forgotPasswordContainer: {
    alignSelf: 'flex-end',
    marginBottom: SPACING.lg,
    marginRight: width * 0.075,
  },
  forgotPasswordText: {
    color: COLORS.primaryGreen,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
  },
  loginButton: {
    marginTop: SPACING.md,
  },
  registerContainer: {
    flexDirection: 'row',
    marginTop: SPACING.xl,
    alignItems: 'center',
  },
  noAccountText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  registerText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
  },
});

export default LoginScreen;
