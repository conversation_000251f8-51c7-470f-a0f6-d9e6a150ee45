/**
 * <PERSON><PERSON>t to copy vector icon fonts to the appropriate directories
 * This script is run after npm install via the postinstall hook
 */
const fs = require('fs');
const path = require('path');

// Define source and destination paths
const VECTOR_ICONS_PATH = path.resolve(
  __dirname,
  'node_modules/react-native-vector-icons/Fonts'
);
const ANDROID_DEST_PATH = path.resolve(
  __dirname,
  'android/app/src/main/assets/fonts'
);
const IOS_DEST_PATH = path.resolve(
  __dirname,
  'ios/LetsTalkApp/Fonts'
);

// Create destination directories if they don't exist
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    console.log(`Creating directory: ${directory}`);
    fs.mkdirSync(directory, { recursive: true });
  }
}

// Copy files from source to destination
function copyFiles(source, destination) {
  ensureDirectoryExists(destination);
  
  if (!fs.existsSync(source)) {
    console.error(`Source directory does not exist: ${source}`);
    return;
  }
  
  const files = fs.readdirSync(source);
  
  files.forEach(file => {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);
    
    try {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`Copied: ${file} to ${destination}`);
    } catch (error) {
      console.error(`Error copying ${file}: ${error.message}`);
    }
  });
}

// Main execution
console.log('Starting vector icons font copy...');

// Copy for Android
copyFiles(VECTOR_ICONS_PATH, ANDROID_DEST_PATH);

// Copy for iOS (if you're developing for iOS)
// Uncomment the line below if you need iOS support
// copyFiles(VECTOR_ICONS_PATH, IOS_DEST_PATH);

console.log('Vector icons font copy completed!');
