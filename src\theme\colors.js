/**
 * App color theme
 */

export const COLORS = {
  // Primary colors
  primaryGreen: '#2E7D32', // Mid-dark green
  primaryDarkGreen: '#1B5E20', // Darker green for contrast
  primaryLightGreen: '#4CAF50', // Lighter green for highlights
  
  // Accent colors
  accentYellow: '#FFD600', // Bright yellow
  accentLightYellow: '#FFEA00', // Lighter yellow for hover states
  
  // Neutral colors
  white: '#FFFFFF',
  offWhite: '#F5F5F5',
  lightGray: '#E0E0E0',
  gray: '#9E9E9E',
  darkGray: '#616161',
  black: '#212121',
  
  // Functional colors
  error: '#D32F2F',
  success: '#388E3C',
  info: '#1976D2',
  warning: '#FFA000',
  
  // Transparent colors
  transparentGreen: 'rgba(46, 125, 50, 0.8)',
  transparentYellow: 'rgba(255, 214, 0, 0.8)',
  transparentBlack: 'rgba(0, 0, 0, 0.5)',
  transparent: 'transparent',
};

export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 30,
  title: 36,
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 40,
};

export const FONT_WEIGHTS = {
  light: '300',
  regular: '400',
  medium: '500',
  semiBold: '600',
  bold: '700',
  extraBold: '800',
};

export default { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS };
