import React from 'react';
import {
  View,
  StyleSheet,
  Pressable
} from 'react-native';
import { COLORS, SPACING } from '../theme/colors';

/**
 * A simplified card component without animations to avoid NaN transform issues
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Function} props.onPress - Function to call when card is pressed
 * @param {Object} props.style - Additional styles to apply to the card
 * @param {number} props.delay - Delay before starting entrance animation (ms) - not used
 * @param {boolean} props.animate - Whether to animate entrance - not used
 * @param {string} props.from - Direction to animate from - not used
 */
const AnimatedCard = ({
  children,
  onPress,
  style,
  delay = 0,
  animate = true,
  from = 'bottom'
}) => {
  // Simplified version without animations

  if (!onPress) {
    return (
      <View style={[styles.card, style]}>
        {children}
      </View>
    );
  }

  return (
    <Pressable
      onPress={onPress}
      style={({pressed}) => [
        styles.card,
        style,
        pressed && styles.pressed
      ]}
    >
      {children}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // Shadow for Android
    elevation: 3,
  },
  pressed: {
    backgroundColor: COLORS.offWhite,
  },
});

export default AnimatedCard;
