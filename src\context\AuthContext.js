import React, { createContext, useState, useEffect, useContext } from 'react';
import {
  getCurrentUser,
  signIn,
  signUp,
  confirmSignUp,
  signOut,
  resendVerificationCode
} from '../services/AuthService';

// Create the Auth Context
const AuthContext = createContext();

// Custom hook to use the Auth Context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Auth Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(null);
  const [needsConfirmation, setNeedsConfirmation] = useState(false);
  const [tempEmail, setTempEmail] = useState('');

  // Check for current user on mount
  useEffect(() => {
    const checkUser = async () => {
      try {
        console.log('Checking for authenticated user...');

        // Add a small delay to ensure Amplify is initialized
        await new Promise(resolve => setTimeout(resolve, 500));

        const result = await getCurrentUser();
        console.log('getCurrentUser result:', result);

        if (result && result.success && result.user) {
          console.log('User authenticated:', result.user);
          setCurrentUser(result.user);
        } else {
          console.log('No authenticated user found');
        }
      } catch (error) {
        console.error('Error checking authenticated user:', error);
      } finally {
        setLoading(false);
      }
    };

    checkUser();
  }, []);

  // Login function
  const login = async (idNumber, password) => {
    setLoading(true);
    setAuthError(null);

    try {
      console.log('Attempting to login with ID Number:', idNumber);
      const result = await signIn(idNumber, password);
      console.log('Login result:', result);

      if (result.success) {
        console.log('Login successful, setting current user');
        setCurrentUser(result.user);
      } else {
        console.log('Login failed:', result.error);
        // Handle special cases
        if (result.userConfirmationRequired) {
          console.log('User confirmation required');
          setNeedsConfirmation(true);
          setTempEmail(idNumber);
        }
        setAuthError(result.error);
      }

      setLoading(false);
      return result;
    } catch (error) {
      console.error('Unexpected error during login:', error);
      setAuthError('An unexpected error occurred. Please try again.');
      setLoading(false);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Register function
  const register = async (idNumber, password, name, email) => {
    setLoading(true);
    setAuthError(null);

    try {
      console.log('Attempting to register with ID Number:', idNumber);
      const result = await signUp(idNumber, password, name, email);
      console.log('Registration result:', result);

      if (result.success) {
        console.log('Registration successful, needs confirmation');
        setNeedsConfirmation(true);
        setTempEmail(idNumber);
      } else {
        console.log('Registration failed:', result.error);
        setAuthError(result.error);
      }

      setLoading(false);
      return result;
    } catch (error) {
      console.error('Unexpected error during registration:', error);
      setAuthError('An unexpected error occurred. Please try again.');
      setLoading(false);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Confirm registration function
  const confirmRegistration = async (idNumber, code) => {
    setLoading(true);
    setAuthError(null);

    const result = await confirmSignUp(idNumber || tempEmail, code);

    if (result.success) {
      setNeedsConfirmation(false);
      setTempEmail('');
    } else {
      setAuthError(result.error);
    }

    setLoading(false);
    return result;
  };

  // Logout function
  const logout = async () => {
    setLoading(true);

    const result = await signOut();

    if (result.success) {
      setCurrentUser(null);
    } else {
      setAuthError(result.error);
    }

    setLoading(false);
    return result;
  };

  // Resend confirmation code
  const resendCode = async (idNumber) => {
    setLoading(true);
    setAuthError(null);

    const result = await resendVerificationCode(idNumber || tempEmail);

    if (!result.success) {
      setAuthError(result.error);
    }

    setLoading(false);
    return result;
  };

  // Reset confirmation state
  const resetConfirmation = () => {
    setNeedsConfirmation(false);
    setTempEmail('');
  };

  // Clear error
  const clearError = () => {
    setAuthError(null);
  };

  // Context value
  const value = {
    currentUser,
    loading,
    authError,
    needsConfirmation,
    tempEmail,
    login,
    register,
    confirmRegistration,
    logout,
    resendCode,
    resetConfirmation,
    clearError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
