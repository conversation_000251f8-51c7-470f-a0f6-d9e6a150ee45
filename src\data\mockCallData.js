// Mock data for call queue and call history
import AsyncStorage from '@react-native-async-storage/async-storage';

// Initial mock call queue
const INITIAL_CALL_QUEUE = [
  {
    id: 'call-1',
    userId: '1',
    userName: 'Thabo Molefi',
    userIdNumber: '9001015678083',
    phoneNumber: '0721234567',
    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
    status: 'waiting',
    estimatedWaitTime: '3 minutes',
    queuePosition: 1
  },
  {
    id: 'call-2',
    userId: '2',
    userName: 'Nomsa Ndlovu',
    userIdNumber: '8502124567084',
    phoneNumber: '0829876543',
    timestamp: new Date(Date.now() - 1000 * 60 * 3).toISOString(), // 3 minutes ago
    status: 'waiting',
    estimatedWaitTime: '5 minutes',
    queuePosition: 2
  },
  {
    id: 'call-3',
    userId: null,
    userName: 'Anonymous User',
    userIdNumber: null,
    phoneNumber: '0731112222',
    timestamp: new Date(Date.now() - 1000 * 60 * 1).toISOString(), // 1 minute ago
    status: 'waiting',
    estimatedWaitTime: '8 minutes',
    queuePosition: 3
  }
];

// Initial mock resolved issues
const INITIAL_RESOLVED_ISSUES = [
  {
    id: 'issue-1',
    callId: 'call-past-1',
    userId: '1',
    userName: 'Thabo Molefi',
    userIdNumber: '9001015678083',
    referenceNumber: 'LT-123456-789',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
    category: 'water',
    description: 'Customer reported water leak in front yard in Soweto',
    resolution: 'Water pipe repaired and leak fixed',
    status: 'resolved',
    handledBy: 'Sipho Nkosi',
    adminId: '3',
    priority: 'medium',
    escalated: false,
    assignedServiceProvider: 'Plumber',
    assignedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2 + 1000 * 60 * 30).toISOString(), // 2 days ago + 30 minutes
    assignedBy: 'Sipho Nkosi',
    resolvedBy: 'Plumber',
    serviceProviderNotes: 'Found a crack in the main water pipe. Replaced the damaged section and tested water flow.',
    resolutionTime: '4 hours 30 minutes'
  },
  {
    id: 'issue-2',
    callId: 'call-past-2',
    userId: '2',
    userName: 'Nomsa Ndlovu',
    userIdNumber: '8502124567084',
    referenceNumber: 'LT-234567-890',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 1).toISOString(), // 1 day ago
    category: 'electricity',
    description: 'Customer reported electricity outage in Sandton area',
    resolution: 'Power lines repaired and electricity restored',
    status: 'resolved',
    handledBy: 'Lerato Moloi',
    adminId: '4',
    priority: 'high',
    escalated: true,
    assignedServiceProvider: 'Power Line Technician',
    assignedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 1 + 1000 * 60 * 15).toISOString(), // 1 day ago + 15 minutes
    assignedBy: 'Lerato Moloi',
    resolvedBy: 'Power Line Technician',
    serviceProviderNotes: 'Downed power line due to storm. Repaired the line and restored power to the affected area.',
    resolutionTime: '6 hours 45 minutes'
  }
];

// Initial mock active issues
const INITIAL_ACTIVE_ISSUES = [
  {
    id: 'issue-3',
    callId: 'call-past-3',
    userId: '1',
    userName: 'Thabo Molefi',
    userIdNumber: '9001015678083',
    referenceNumber: 'LT-345678-901',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(), // 12 hours ago
    category: 'sanitation',
    description: 'Customer reported blocked drain causing flooding in Diepkloof',
    resolution: null,
    status: 'in-progress',
    handledBy: 'Sipho Nkosi',
    adminId: '3',
    priority: 'medium',
    escalated: false,
    assignedServiceProvider: 'Drain Cleaning Specialist',
    assignedAt: new Date(Date.now() - 1000 * 60 * 60 * 10).toISOString(), // 10 hours ago
    assignedBy: 'Sipho Nkosi'
  },
  {
    id: 'issue-4',
    callId: 'call-past-4',
    userId: '2',
    userName: 'Nomsa Ndlovu',
    userIdNumber: '8502124567084',
    referenceNumber: 'LT-456789-012',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
    category: 'roads',
    description: 'Customer reported large pothole on Mandela Drive causing damage to vehicles',
    resolution: null,
    status: 'escalated',
    handledBy: 'Lerato Moloi',
    adminId: '4',
    priority: 'high',
    escalated: true,
    escalationReason: 'Requires immediate attention due to safety hazard',
    assignedServiceProvider: 'Pothole Repair Crew',
    assignedAt: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
    assignedBy: 'Lerato Moloi'
  },
  {
    id: 'issue-5',
    callId: 'call-past-5',
    userId: '3',
    userName: 'Bongani Khumalo',
    userIdNumber: '8909125678087',
    referenceNumber: 'LT-567890-123',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
    category: 'fire',
    description: 'Customer reported smoke coming from abandoned building in Alexandra',
    resolution: null,
    status: 'in-progress',
    handledBy: 'Sipho Nkosi',
    adminId: '3',
    priority: 'high',
    escalated: false,
    assignedServiceProvider: 'Firefighter Team A',
    assignedAt: new Date(Date.now() - 1000 * 60 * 60 * 2.5).toISOString(), // 2.5 hours ago
    assignedBy: 'Sipho Nkosi'
  }
];

// Categories for call classification
export const CALL_CATEGORIES = [
  {
    id: 'water',
    name: 'Water Issue',
    serviceProviders: [
      'Water Technician',
      'Plumber',
      'Pipe Repair Specialist',
      'Water Meter Technician',
      'Water Quality Inspector'
    ]
  },
  {
    id: 'electricity',
    name: 'Electrical Issue',
    serviceProviders: [
      'Electrician',
      'Power Line Technician',
      'Electrical Engineer',
      'Substation Technician',
      'Electrical Safety Inspector'
    ]
  },
  {
    id: 'fire',
    name: 'Fire Emergency',
    serviceProviders: [
      'Firefighter Team A',
      'Firefighter Team B',
      'Firefighter Team C',
      'Fire Safety Inspector',
      'Emergency Response Unit'
    ]
  },
  {
    id: 'roads',
    name: 'Road Infrastructure',
    serviceProviders: [
      'Pothole Repair Crew',
      'Road Maintenance Team',
      'Traffic Signal Technician',
      'Street Lighting Technician',
      'Road Construction Team'
    ]
  },
  {
    id: 'sanitation',
    name: 'Sanitation Issue',
    serviceProviders: [
      'Sewage Technician',
      'Waste Collection Team',
      'Drain Cleaning Specialist',
      'Sanitation Inspector',
      'Waste Management Technician'
    ]
  },
  {
    id: 'building',
    name: 'Building Issue',
    serviceProviders: [
      'Building Inspector',
      'Construction Technician',
      'Structural Engineer',
      'Roof Repair Specialist',
      'Building Maintenance Team'
    ]
  },
  {
    id: 'parks',
    name: 'Parks & Recreation',
    serviceProviders: [
      'Parks Maintenance Team',
      'Landscaping Crew',
      'Playground Inspector',
      'Tree Trimming Specialist',
      'Recreation Facility Technician'
    ]
  },
  {
    id: 'security',
    name: 'Security Concern',
    serviceProviders: [
      'Security Officer',
      'Community Patrol',
      'Security Camera Technician',
      'Street Light Repair Team',
      'Community Safety Inspector'
    ]
  },
  {
    id: 'animals',
    name: 'Animal Control',
    serviceProviders: [
      'Animal Control Officer',
      'Wildlife Specialist',
      'Pest Control Technician',
      'Veterinary Services',
      'Animal Shelter Team'
    ]
  },
  {
    id: 'other',
    name: 'Other',
    serviceProviders: [
      'General Maintenance Technician',
      'Municipal Services Team',
      'Special Projects Crew',
      'Emergency Response Technician',
      'Community Services Team'
    ]
  }
];

// Priority levels
export const PRIORITY_LEVELS = [
  { id: 'low', name: 'Low', color: '#4CAF50' },
  { id: 'medium', name: 'Medium', color: '#FFC107' },
  { id: 'high', name: 'High', color: '#F44336' },
  { id: 'critical', name: 'Critical', color: '#9C27B0' }
];

// Standard questions for call handling
export const STANDARD_QUESTIONS = [
  {
    id: 'location',
    question: 'What is your current location?',
    type: 'text',
    required: true
  },
  {
    id: 'issue-type',
    question: 'What type of issue are you experiencing?',
    type: 'select',
    options: CALL_CATEGORIES.map(cat => ({ value: cat.id, label: cat.name })),
    required: true
  },
  {
    id: 'description',
    question: 'Please describe your issue in detail',
    type: 'textarea',
    required: true
  },
  {
    id: 'duration',
    question: 'How long have you been experiencing this issue?',
    type: 'text',
    required: false
  },
  {
    id: 'previous-contact',
    question: 'Have you previously contacted us about this issue?',
    type: 'radio',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' }
    ],
    required: true
  },
  {
    id: 'reference-number',
    question: 'If yes, what was your reference number?',
    type: 'text',
    required: false,
    conditionalOn: { field: 'previous-contact', value: 'yes' }
  },
  {
    id: 'urgency',
    question: 'How urgent is this issue?',
    type: 'select',
    options: [
      { value: 'not-urgent', label: 'Not Urgent' },
      { value: 'somewhat-urgent', label: 'Somewhat Urgent' },
      { value: 'very-urgent', label: 'Very Urgent' },
      { value: 'emergency', label: 'Emergency' }
    ],
    required: true
  }
];

// Initialize the mock data in AsyncStorage
export const initializeMockCallData = async () => {
  try {
    // Check if call queue already exists
    const queueExists = await AsyncStorage.getItem('@call_queue');
    if (!queueExists) {
      await AsyncStorage.setItem('@call_queue', JSON.stringify(INITIAL_CALL_QUEUE));
    }

    // Check if resolved issues already exist
    const resolvedExists = await AsyncStorage.getItem('@resolved_issues');
    if (!resolvedExists) {
      await AsyncStorage.setItem('@resolved_issues', JSON.stringify(INITIAL_RESOLVED_ISSUES));
    }

    // Check if active issues already exist
    const activeExists = await AsyncStorage.getItem('@active_issues');
    if (!activeExists) {
      await AsyncStorage.setItem('@active_issues', JSON.stringify(INITIAL_ACTIVE_ISSUES));
    }

    console.log('Mock call data initialized successfully');
  } catch (error) {
    console.error('Error initializing mock call data:', error);
  }
};

// Get the current call queue
export const getCallQueue = async () => {
  try {
    const queueJson = await AsyncStorage.getItem('@call_queue');
    return queueJson ? JSON.parse(queueJson) : [];
  } catch (error) {
    console.error('Error getting call queue:', error);
    return [];
  }
};

// Add a call to the queue
export const addCallToQueue = async (callData) => {
  try {
    const queue = await getCallQueue();
    const newCall = {
      id: `call-${Date.now()}`,
      timestamp: new Date().toISOString(),
      status: 'waiting',
      queuePosition: queue.length + 1,
      estimatedWaitTime: `${Math.floor(Math.random() * 10) + 1} minutes`,
      ...callData
    };

    const updatedQueue = [...queue, newCall];
    await AsyncStorage.setItem('@call_queue', JSON.stringify(updatedQueue));
    return newCall;
  } catch (error) {
    console.error('Error adding call to queue:', error);
    return null;
  }
};

// Update a call in the queue
export const updateCallInQueue = async (callId, updates) => {
  try {
    const queue = await getCallQueue();
    const updatedQueue = queue.map(call =>
      call.id === callId ? { ...call, ...updates } : call
    );

    await AsyncStorage.setItem('@call_queue', JSON.stringify(updatedQueue));
    return updatedQueue.find(call => call.id === callId);
  } catch (error) {
    console.error('Error updating call in queue:', error);
    return null;
  }
};

// Remove a call from the queue
export const removeCallFromQueue = async (callId) => {
  try {
    const queue = await getCallQueue();
    const updatedQueue = queue.filter(call => call.id !== callId);

    // Update queue positions
    const reorderedQueue = updatedQueue.map((call, index) => ({
      ...call,
      queuePosition: index + 1
    }));

    await AsyncStorage.setItem('@call_queue', JSON.stringify(reorderedQueue));
    return true;
  } catch (error) {
    console.error('Error removing call from queue:', error);
    return false;
  }
};

// Get active issues
export const getActiveIssues = async () => {
  try {
    const issuesJson = await AsyncStorage.getItem('@active_issues');
    return issuesJson ? JSON.parse(issuesJson) : [];
  } catch (error) {
    console.error('Error getting active issues:', error);
    return [];
  }
};

// Get resolved issues
export const getResolvedIssues = async () => {
  try {
    const issuesJson = await AsyncStorage.getItem('@resolved_issues');
    return issuesJson ? JSON.parse(issuesJson) : [];
  } catch (error) {
    console.error('Error getting resolved issues:', error);
    return [];
  }
};

// Add a new issue
export const addIssue = async (issueData) => {
  try {
    const activeIssues = await getActiveIssues();
    const newIssue = {
      id: `issue-${Date.now()}`,
      timestamp: new Date().toISOString(),
      status: 'new',
      escalated: false,
      ...issueData
    };

    const updatedIssues = [...activeIssues, newIssue];
    await AsyncStorage.setItem('@active_issues', JSON.stringify(updatedIssues));
    return newIssue;
  } catch (error) {
    console.error('Error adding issue:', error);
    return null;
  }
};

// Update an issue
export const updateIssue = async (issueId, updates) => {
  try {
    const activeIssues = await getActiveIssues();
    const resolvedIssues = await getResolvedIssues();

    // Check if the issue is in active issues
    const activeIndex = activeIssues.findIndex(issue => issue.id === issueId);

    if (activeIndex !== -1) {
      const updatedIssue = { ...activeIssues[activeIndex], ...updates };

      // If the issue is now resolved, move it to resolved issues
      if (updates.status === 'resolved') {
        const newActiveIssues = activeIssues.filter(issue => issue.id !== issueId);
        const newResolvedIssues = [...resolvedIssues, updatedIssue];

        await AsyncStorage.setItem('@active_issues', JSON.stringify(newActiveIssues));
        await AsyncStorage.setItem('@resolved_issues', JSON.stringify(newResolvedIssues));
      } else {
        // Otherwise, just update it in active issues
        const newActiveIssues = [...activeIssues];
        newActiveIssues[activeIndex] = updatedIssue;

        await AsyncStorage.setItem('@active_issues', JSON.stringify(newActiveIssues));
      }

      return updatedIssue;
    }

    // Check if the issue is in resolved issues
    const resolvedIndex = resolvedIssues.findIndex(issue => issue.id === issueId);

    if (resolvedIndex !== -1) {
      const updatedIssue = { ...resolvedIssues[resolvedIndex], ...updates };

      // If the issue is no longer resolved, move it back to active issues
      if (updates.status !== 'resolved') {
        const newResolvedIssues = resolvedIssues.filter(issue => issue.id !== issueId);
        const newActiveIssues = [...activeIssues, updatedIssue];

        await AsyncStorage.setItem('@resolved_issues', JSON.stringify(newResolvedIssues));
        await AsyncStorage.setItem('@active_issues', JSON.stringify(newActiveIssues));
      } else {
        // Otherwise, just update it in resolved issues
        const newResolvedIssues = [...resolvedIssues];
        newResolvedIssues[resolvedIndex] = updatedIssue;

        await AsyncStorage.setItem('@resolved_issues', JSON.stringify(newResolvedIssues));
      }

      return updatedIssue;
    }

    return null;
  } catch (error) {
    console.error('Error updating issue:', error);
    return null;
  }
};

// Generate a reference number
export const generateReferenceNumber = () => {
  const prefix = 'LT';
  const timestamp = new Date().getTime().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
};

export default {
  initializeMockCallData,
  getCallQueue,
  addCallToQueue,
  updateCallInQueue,
  removeCallFromQueue,
  getActiveIssues,
  getResolvedIssues,
  addIssue,
  updateIssue,
  generateReferenceNumber,
  CALL_CATEGORIES,
  PRIORITY_LEVELS,
  STANDARD_QUESTIONS
};
