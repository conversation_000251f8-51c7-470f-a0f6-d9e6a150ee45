import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

const RDPApplicationScreen = ({ navigation }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    idNumber: '',
    phoneNumber: '',
    email: '',
    maritalStatus: '',
    
    // Address Information
    currentAddress: '',
    province: '',
    municipality: '',
    
    // Employment Information
    employmentStatus: '',
    monthlyIncome: '',
    employer: '',
    
    // Household Information
    householdSize: '',
    dependants: '',
    dependantDetails: [],
    
    // Housing Information
    currentHousingType: '',
    previouslyOwnedProperty: false,
    receivedHousingSubsidy: false,
    
    // Preferences
    preferredProvince: '',
    preferredMunicipality: '',
  });

  const totalSteps = 4;

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addDependant = () => {
    const newDependant = {
      id: Date.now(),
      name: '',
      relationship: '',
      age: '',
      idNumber: '',
    };
    setFormData(prev => ({
      ...prev,
      dependantDetails: [...prev.dependantDetails, newDependant]
    }));
  };

  const updateDependant = (id, field, value) => {
    setFormData(prev => ({
      ...prev,
      dependantDetails: prev.dependantDetails.map(dep =>
        dep.id === id ? { ...dep, [field]: value } : dep
      )
    }));
  };

  const removeDependant = (id) => {
    setFormData(prev => ({
      ...prev,
      dependantDetails: prev.dependantDetails.filter(dep => dep.id !== id)
    }));
  };

  const validateStep = (step) => {
    switch (step) {
      case 1:
        return formData.firstName && formData.lastName && formData.idNumber && formData.phoneNumber;
      case 2:
        return formData.currentAddress && formData.province && formData.municipality;
      case 3:
        return formData.employmentStatus && formData.monthlyIncome;
      case 4:
        return formData.householdSize && formData.preferredProvince;
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      if (currentStep < totalSteps) {
        setCurrentStep(currentStep + 1);
      } else {
        submitApplication();
      }
    } else {
      Alert.alert('Incomplete Information', 'Please fill in all required fields before proceeding.');
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const submitApplication = () => {
    Alert.alert(
      'Application Submitted',
      'Your RDP housing application has been submitted successfully. You will receive a reference number via SMS shortly.',
      [
        {
          text: 'OK',
          onPress: () => navigation.navigate('RDPStatus', { newApplication: true })
        }
      ]
    );
  };

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <Text style={styles.progressText}>Step {currentStep} of {totalSteps}</Text>
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill, 
            { width: `${(currentStep / totalSteps) * 100}%` }
          ]} 
        />
      </View>
    </View>
  );

  const renderPersonalInfo = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Personal Information</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>First Name *</Text>
        <TextInput
          style={styles.input}
          value={formData.firstName}
          onChangeText={(value) => updateFormData('firstName', value)}
          placeholder="Enter your first name"
          placeholderTextColor={COLORS.gray}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Last Name *</Text>
        <TextInput
          style={styles.input}
          value={formData.lastName}
          onChangeText={(value) => updateFormData('lastName', value)}
          placeholder="Enter your last name"
          placeholderTextColor={COLORS.gray}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>ID Number *</Text>
        <TextInput
          style={styles.input}
          value={formData.idNumber}
          onChangeText={(value) => updateFormData('idNumber', value)}
          placeholder="Enter your 13-digit ID number"
          placeholderTextColor={COLORS.gray}
          keyboardType="numeric"
          maxLength={13}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Phone Number *</Text>
        <TextInput
          style={styles.input}
          value={formData.phoneNumber}
          onChangeText={(value) => updateFormData('phoneNumber', value)}
          placeholder="Enter your phone number"
          placeholderTextColor={COLORS.gray}
          keyboardType="phone-pad"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Email Address</Text>
        <TextInput
          style={styles.input}
          value={formData.email}
          onChangeText={(value) => updateFormData('email', value)}
          placeholder="Enter your email address"
          placeholderTextColor={COLORS.gray}
          keyboardType="email-address"
          autoCapitalize="none"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Marital Status</Text>
        <View style={styles.radioGroup}>
          {['Single', 'Married', 'Divorced', 'Widowed', 'Cohabiting'].map((status) => (
            <TouchableOpacity
              key={status}
              style={styles.radioOption}
              onPress={() => updateFormData('maritalStatus', status)}
            >
              <View style={[
                styles.radioCircle,
                formData.maritalStatus === status && styles.radioSelected
              ]} />
              <Text style={styles.radioText}>{status}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderAddressInfo = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Current Address</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Current Address *</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.currentAddress}
          onChangeText={(value) => updateFormData('currentAddress', value)}
          placeholder="Enter your current residential address"
          placeholderTextColor={COLORS.gray}
          multiline
          numberOfLines={3}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Province *</Text>
        <View style={styles.pickerContainer}>
          {['Eastern Cape', 'Free State', 'Gauteng', 'KwaZulu-Natal', 'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West', 'Western Cape'].map((province) => (
            <TouchableOpacity
              key={province}
              style={[
                styles.pickerOption,
                formData.province === province && styles.pickerSelected
              ]}
              onPress={() => updateFormData('province', province)}
            >
              <Text style={[
                styles.pickerText,
                formData.province === province && styles.pickerSelectedText
              ]}>{province}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Municipality *</Text>
        <TextInput
          style={styles.input}
          value={formData.municipality}
          onChangeText={(value) => updateFormData('municipality', value)}
          placeholder="Enter your municipality"
          placeholderTextColor={COLORS.gray}
        />
      </View>
    </View>
  );

  const renderEmploymentInfo = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Employment & Income</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Employment Status *</Text>
        <View style={styles.radioGroup}>
          {['Employed', 'Self-employed', 'Unemployed', 'Pensioner', 'Student'].map((status) => (
            <TouchableOpacity
              key={status}
              style={styles.radioOption}
              onPress={() => updateFormData('employmentStatus', status)}
            >
              <View style={[
                styles.radioCircle,
                formData.employmentStatus === status && styles.radioSelected
              ]} />
              <Text style={styles.radioText}>{status}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Monthly Household Income *</Text>
        <TextInput
          style={styles.input}
          value={formData.monthlyIncome}
          onChangeText={(value) => updateFormData('monthlyIncome', value)}
          placeholder="Enter total monthly household income"
          placeholderTextColor={COLORS.gray}
          keyboardType="numeric"
        />
        <Text style={styles.helperText}>Must be below R3,500 to qualify for RDP housing</Text>
      </View>

      {formData.employmentStatus === 'Employed' && (
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Employer Name</Text>
          <TextInput
            style={styles.input}
            value={formData.employer}
            onChangeText={(value) => updateFormData('employer', value)}
            placeholder="Enter your employer's name"
            placeholderTextColor={COLORS.gray}
          />
        </View>
      )}
    </View>
  );

  const renderHouseholdInfo = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Household & Housing Information</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Household Size *</Text>
        <TextInput
          style={styles.input}
          value={formData.householdSize}
          onChangeText={(value) => updateFormData('householdSize', value)}
          placeholder="Number of people in your household"
          placeholderTextColor={COLORS.gray}
          keyboardType="numeric"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Number of Dependants</Text>
        <TextInput
          style={styles.input}
          value={formData.dependants}
          onChangeText={(value) => updateFormData('dependants', value)}
          placeholder="Number of dependants (children, elderly, etc.)"
          placeholderTextColor={COLORS.gray}
          keyboardType="numeric"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Current Housing Type</Text>
        <View style={styles.radioGroup}>
          {['Informal settlement', 'Backyard dwelling', 'Rented accommodation', 'Family home', 'Other'].map((type) => (
            <TouchableOpacity
              key={type}
              style={styles.radioOption}
              onPress={() => updateFormData('currentHousingType', type)}
            >
              <View style={[
                styles.radioCircle,
                formData.currentHousingType === type && styles.radioSelected
              ]} />
              <Text style={styles.radioText}>{type}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Preferred Province for Housing *</Text>
        <View style={styles.pickerContainer}>
          {['Eastern Cape', 'Free State', 'Gauteng', 'KwaZulu-Natal', 'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West', 'Western Cape'].map((province) => (
            <TouchableOpacity
              key={province}
              style={[
                styles.pickerOption,
                formData.preferredProvince === province && styles.pickerSelected
              ]}
              onPress={() => updateFormData('preferredProvince', province)}
            >
              <Text style={[
                styles.pickerText,
                formData.preferredProvince === province && styles.pickerSelectedText
              ]}>{province}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderPersonalInfo();
      case 2:
        return renderAddressInfo();
      case 3:
        return renderEmploymentInfo();
      case 4:
        return renderHouseholdInfo();
      default:
        return renderPersonalInfo();
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>RDP Application</Text>
        <View style={styles.placeholder} />
      </View>

      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {renderProgressBar()}
        
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {renderCurrentStep()}
        </ScrollView>

        {/* Navigation Buttons */}
        <View style={styles.navigationContainer}>
          {currentStep > 1 && (
            <CustomButton
              title="Previous"
              onPress={prevStep}
              type="outline"
              style={styles.navButton}
            />
          )}
          <CustomButton
            title={currentStep === totalSteps ? 'Submit Application' : 'Next'}
            onPress={nextStep}
            style={[styles.navButton, currentStep === 1 && styles.fullWidthButton]}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  placeholder: {
    width: 40,
  },
  keyboardView: {
    flex: 1,
  },
  progressContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  progressText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  progressBar: {
    height: 4,
    backgroundColor: COLORS.lightGray,
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.md,
  },
  stepContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  stepTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 8,
    padding: SPACING.sm,
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    backgroundColor: COLORS.white,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
    marginTop: SPACING.xs,
  },
  radioGroup: {
    flexDirection: 'column',
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.lightGray,
    marginRight: SPACING.sm,
  },
  radioSelected: {
    borderColor: COLORS.primaryGreen,
    backgroundColor: COLORS.primaryGreen,
  },
  radioText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
  },
  pickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  pickerOption: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    backgroundColor: COLORS.white,
  },
  pickerSelected: {
    backgroundColor: COLORS.primaryGreen,
    borderColor: COLORS.primaryGreen,
  },
  pickerText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
  },
  pickerSelectedText: {
    color: COLORS.white,
  },
  navigationContainer: {
    flexDirection: 'row',
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    gap: SPACING.sm,
  },
  navButton: {
    flex: 1,
  },
  fullWidthButton: {
    flex: 1,
  },
});

export default RDPApplicationScreen;
