import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import { COLORS, FONT_SIZES, FONT_WEIGHTS } from '../theme/colors';
import { IMAGES } from '../assets/images';
import { useMockAuth } from '../context/MockAuthContext';

const SplashScreen = ({ navigation }) => {
  // Get current user from auth context
  const { currentUser } = useMockAuth();

  useEffect(() => {
    // Use a simple timeout instead of InteractionManager
    const timer = setTimeout(() => {
      // Navigate based on authentication and user role
      if (currentUser) {
        if (currentUser.role === 'admin') {
          navigation.replace('AdminDashboard');
        } else if (currentUser.role === 'doctor') {
          navigation.replace('DoctorDashboard');
        } else {
          navigation.replace('Main');
        }
      } else {
        navigation.replace('Login');
      }
    }, 1500);

    // Cleanup function
    return () => {
      clearTimeout(timer);
    };
  }, [currentUser, navigation]);

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <View style={styles.logoContainer}>
          <Image
            source={IMAGES.logo}
            style={styles.logo}
            resizeMode="contain"
            accessibilityLabel="Let's Talk Logo"
          />
        </View>

        <View style={styles.textContainer}>
          <Text style={styles.appName}>Let's Talk</Text>
          <Text style={styles.tagline}>Connect with government services</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    // Avoid layout issues on Android
    ...Platform.select({
      android: {
        elevation: 0,
      },
    }),
  },
  logo: {
    width: 200,
    height: 200,
  },
  textContainer: {
    alignItems: 'center',
    // Avoid layout issues on Android
    ...Platform.select({
      android: {
        elevation: 0,
      },
    }),
  },
  appName: {
    fontSize: FONT_SIZES.title,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: 10,
  },
  tagline: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
});

export default SplashScreen;
