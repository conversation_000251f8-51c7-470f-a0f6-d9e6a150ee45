{"name": "LetsTalkApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "postinstall": "node copy-vector-icons.js"}, "dependencies": {"@aws-amplify/react-native": "^1.1.9", "@aws-amplify/ui-react-native": "^2.5.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "amazon-cognito-identity-js": "^6.3.15", "aws-amplify": "^6.14.4", "react": "19.0.0", "react-native": "0.79.1", "react-native-get-random-values": "^1.11.0", "react-native-maps": "^1.23.8", "react-native-pure-chart": "^0.0.24", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-vector-icons": "^10.2.0", "whatwg-url": "^14.2.0", "whatwg-url-without-unicode": "^8.0.0-3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "sharp": "^0.34.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}