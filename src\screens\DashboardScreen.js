import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Platform,
  Alert,
  Animated,
  RefreshControl,
  Easing,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import { useMockAuth } from '../context/MockAuthContext';
import CustomButton from '../components/CustomButton';
import GradientBackground from '../components/GradientBackground';
import SkeletonLoader from '../components/SkeletonLoader';
import AnimatedCard from '../components/AnimatedCard';
import WeatherWidget from '../components/WeatherWidget';

// --- Constants for Types/Status (Good Practice) ---
const SERVICE_STATUS_TYPES = {
  NORMAL: 'normal',
  WARNING: 'warning',
  ISSUE: 'issue',
};

const ALERT_TYPES = {
  MAINTENANCE: 'maintenance',
  BILL: 'bill',
  COMMUNITY: 'community',
  GENERAL: 'general', // Added a default type
};

// --- Updated Mock Data ---
// Note: Dates are relative to April 30, 2025 (adjust as needed)
const RECENT_ALERTS = [
  {
    id: '1',
    title: 'Water Maintenance',
    message: 'Scheduled water maintenance in your area on May 5th, 7:00-12:00.',
    date: '2025-04-30T10:00:00Z', // Use ISO strings for easier Date object creation
    type: ALERT_TYPES.MAINTENANCE,
    read: false,
  },
  {
    id: '2',
    title: 'Electricity Bill Due Soon',
    message: 'Your electricity bill of R750.00 is due on May 10th.',
    date: '2025-04-29T15:30:00Z',
    type: ALERT_TYPES.BILL,
    read: true,
  },
  {
    id: '3',
    title: 'Community Meeting Reminder',
    message: 'Community meeting scheduled for May 8th at the town hall.',
    date: '2025-04-28T09:00:00Z',
    type: ALERT_TYPES.COMMUNITY,
    read: false,
  },
];

const SERVICES_STATUS = [
  { id: '1', name: 'Water', status: SERVICE_STATUS_TYPES.NORMAL },
  { id: '2', name: 'Electricity', status: SERVICE_STATUS_TYPES.WARNING }, // Example warning
  { id: '3', name: 'Waste', status: SERVICE_STATUS_TYPES.NORMAL },
  { id: '4', name: 'Roads', status: SERVICE_STATUS_TYPES.ISSUE }, // Example issue
];

// --- Helper for Cross-Platform Shadows ---
const shadowStyle = {
  // Android
  elevation: 3,
  // iOS
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.22,
  shadowRadius: 2.22,
};

const DashboardScreen = ({ navigation }) => {
  const { currentUser, signOut } = useMockAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dataLoaded, setDataLoaded] = useState({
    alerts: false,
    services: false,
    weather: false
  });

  // Removed animation values to avoid NaN issues

  // Simulate data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Simulate loading individual sections with delays
  useEffect(() => {
    if (loading) return;

    const timers = [
      setTimeout(() => setDataLoaded(prev => ({ ...prev, alerts: true })), 300),
      setTimeout(() => setDataLoaded(prev => ({ ...prev, services: true })), 600),
      setTimeout(() => setDataLoaded(prev => ({ ...prev, weather: true })), 900),
    ];

    return () => timers.forEach(timer => clearTimeout(timer));
  }, [loading]);

  // Handle pull-to-refresh
  const onRefresh = React.useCallback(() => {
    setRefreshing(true);

    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  // --- Navigation Handler ---
  const navigateToSection = (section, params = {}) => {
    console.log(`Navigating to ${section} with params:`, params);

    // Map section names to actual screen names
    const screenMapping = {
      'PayBillsScreen': 'Utilities',
      'AlertsScreen': 'Services',
      'CommunityHubScreen': 'Community',
      'MyAccountScreen': 'Profile',
      'ThusongAIScreen': 'ThusongAI',
      'HealthServicesScreen': 'HealthServices'
    };

    // Add haptic feedback here if available

    // Navigate to the appropriate screen
    if (screenMapping[section]) {
      navigation.navigate(screenMapping[section], params);
    } else {
      // Fallback for screens not yet implemented
      Alert.alert('Navigation', `Navigating to ${section}`);
    }
  };

  // --- Alert Interaction Handler ---
  const handleAlertPress = (alert) => {
    console.log('Alert pressed:', alert.id);

    // Navigate based on alert type
    switch (alert.type) {
      case ALERT_TYPES.MAINTENANCE:
        navigateToSection('AlertsScreen', { alertId: alert.id, category: 'maintenance' });
        break;
      case ALERT_TYPES.BILL:
        navigateToSection('PayBillsScreen', { alertId: alert.id, category: 'bill' });
        break;
      case ALERT_TYPES.COMMUNITY:
        navigateToSection('CommunityHubScreen', { alertId: alert.id, category: 'community' });
        break;
      default:
        navigateToSection('AlertsScreen', { alertId: alert.id });
    }

    // Mark alert as read (in a real app, this would update state or call an API)
    // For now, we'll just show a message
    Alert.alert('Alert Marked as Read', `"${alert.title}" has been marked as read.`);
  };


  const handleLogout = async () => {
    try {
      const result = await signOut();
      if (result.success) {
        // Explicitly navigate to Login screen
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        });
      } else {
        Alert.alert('Logout Failed', 'Could not log out. Please try again.');
      }
    } catch (error) {
      console.error('Error during logout:', error);
      Alert.alert('Error', 'An unexpected error occurred during logout.');
    }
  };

  // --- Render Alert Item ---
  const renderAlertItem = ({ item }) => {
    let iconName = 'bell-outline'; // Default icon
    let iconColor = COLORS.info;

    switch (item.type) {
      case ALERT_TYPES.MAINTENANCE:
        iconName = 'wrench';
        iconColor = COLORS.warning;
        break;
      case ALERT_TYPES.BILL:
        iconName = 'receipt';
        iconColor = COLORS.primaryGreen;
        break;
      case ALERT_TYPES.COMMUNITY:
        iconName = 'account-group';
        iconColor = COLORS.info;
        break;
    }

    return (
      <TouchableOpacity
        style={[styles.alertItem, !item.read && styles.unreadAlert]}
        onPress={() => handleAlertPress(item)} // Call refined handler
        activeOpacity={0.7}
      >
        <View style={[styles.alertIconContainer, { backgroundColor: iconColor }]}>
          <Icon name={iconName} size={22} color={COLORS.white} />
        </View>
        <View style={styles.alertContent}>
          <Text style={styles.alertTitle}>{item.title}</Text>
          <Text style={styles.alertMessage} numberOfLines={2}>
            {item.message}
          </Text>
          <Text style={styles.alertDate}>
            {/* Format date more nicely */}
            {new Date(item.date).toLocaleDateString(undefined, { // Use locale default format
              year: 'numeric', month: 'short', day: 'numeric'
             })}
          </Text>
        </View>
        {!item.read && <View style={styles.unreadDot} />}
      </TouchableOpacity>
    );
  };

  // --- Render Service Status ---
  const renderServiceStatus = () => {
    if (loading) {
      return (
        <View style={[styles.cardContainer, shadowStyle, { padding: 0 }]}>
          <View style={{ padding: SPACING.md }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.md }}>
              <SkeletonLoader width={20} height={20} borderRadius={10} />
              <SkeletonLoader
                width={120}
                height={18}
                borderRadius={4}
                style={{ marginLeft: SPACING.sm }}
              />
            </View>
            {[1, 2, 3, 4].map((item) => (
              <View key={item} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.sm }}>
                <SkeletonLoader width={12} height={12} borderRadius={6} />
                <SkeletonLoader
                  width={80}
                  height={16}
                  borderRadius={4}
                  style={{ marginLeft: SPACING.sm, marginRight: 'auto' }}
                />
              </View>
            ))}
          </View>
        </View>
      );
    }

    return (
      <AnimatedCard
        style={styles.cardContainer}
        animate={!loading && dataLoaded.services}
        from="right"
        delay={800}
      >
        <View style={styles.sectionTitleContainer}>
          <Icon name="check-circle-outline" size={20} color={COLORS.primaryGreen} />
          <Text style={styles.sectionTitle}>Service Status</Text>
        </View>
        <View style={styles.serviceStatusGrid}>
          {SERVICES_STATUS.map((service, index) => {
            // Calculate animation delay based on index
            const itemDelay = 900 + (index * 100);

            return (
              <Animated.View
                key={service.id}
                style={[
                  styles.serviceStatusItem,
                  {
                    opacity: dataLoaded.services ? 1 : 0,
                    transform: [{
                      translateX: dataLoaded.services ? 0 : 20
                    }],
                  }
                ]}
              >
                <View
                  style={[
                    styles.statusIndicator,
                    service.status === SERVICE_STATUS_TYPES.NORMAL && styles.statusNormal,
                    service.status === SERVICE_STATUS_TYPES.WARNING && styles.statusWarning,
                    service.status === SERVICE_STATUS_TYPES.ISSUE && styles.statusIssue,
                  ]}
                />
                <Text style={styles.serviceStatusText}>{service.name}</Text>
                {service.status !== SERVICE_STATUS_TYPES.NORMAL && (
                  <TouchableOpacity
                    style={styles.statusInfoButton}
                    onPress={() => navigateToSection('AlertsScreen', { service: service.name })}
                  >
                    <Icon
                      name="information-outline"
                      size={16}
                      color={service.status === SERVICE_STATUS_TYPES.WARNING ? COLORS.warning : COLORS.error}
                    />
                  </TouchableOpacity>
                )}
              </Animated.View>
            );
          })}
        </View>
      </AnimatedCard>
    );
  };

  // Render skeleton loaders for cards
  const renderSkeletonCard = () => (
    <View style={[styles.cardContainer, shadowStyle, { padding: 0 }]}>
      <View style={{ padding: SPACING.md }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.md }}>
          <SkeletonLoader width={20} height={20} borderRadius={10} />
          <SkeletonLoader
            width={120}
            height={18}
            borderRadius={4}
            style={{ marginLeft: SPACING.sm }}
          />
        </View>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <SkeletonLoader width={'48%'} height={80} borderRadius={10} />
          <SkeletonLoader width={'48%'} height={80} borderRadius={10} />
        </View>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: SPACING.sm }}>
          <SkeletonLoader width={'48%'} height={80} borderRadius={10} />
          <SkeletonLoader width={'48%'} height={80} borderRadius={10} />
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Dashboard Header with Gradient */}
      <View style={styles.screenHeader}>
        <GradientBackground
          colors={[COLORS.primaryGreen, COLORS.primaryDarkGreen]}
          style={styles.headerGradient}
        >
          <Text style={styles.screenHeaderTitle}>
            Dashboard
          </Text>
          <Text style={styles.headerSubtitle}>
            {new Date().toLocaleDateString('en-ZA', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </Text>
        </GradientBackground>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.primaryGreen]}
            tintColor={COLORS.primaryGreen}
            title="Pull to refresh"
            titleColor={COLORS.darkGray}
          />
        }
      >
        {/* User Welcome Card */}
        <AnimatedCard
          style={styles.header}
          animate={!loading}
          from="top"
          delay={100}
        >
          <View style={styles.userInfo}>
            <Text style={styles.welcomeText}>
              Welcome, {currentUser?.name?.split(' ')[0] || 'User'}
            </Text>
            {currentUser?.idNumber && (
              <Text style={styles.idText}>
                ID: {currentUser.idNumber.replace(/(\d{6})(\d{4})(\d{3})/, '$1 $2 $3')}
              </Text>
            )}
            <View style={styles.lastLoginContainer}>
              <Icon name="clock-outline" size={14} color={COLORS.darkGray} />
              <Text style={styles.lastLoginText}>Last login: Today, 09:45 AM</Text>
            </View>
          </View>
          <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
            <Icon name="logout" size={24} color={COLORS.primaryGreen} />
          </TouchableOpacity>
        </AnimatedCard>

        {/* Weather Widget */}
        <AnimatedCard
          style={styles.weatherWidgetContainer}
          animate={!loading}
          from="right"
          delay={200}
        >
          <WeatherWidget />
        </AnimatedCard>

        {/* Quick Actions with Enhanced Styling */}
        {loading ? renderSkeletonCard() : (
          <AnimatedCard
            style={styles.cardContainer}
            animate={!loading}
            from="left"
            delay={300}
          >
            <View style={styles.sectionTitleContainer}>
              <Icon name="lightning-bolt" size={20} color={COLORS.primaryGreen} />
              <Text style={styles.sectionTitle}>Quick Actions</Text>
            </View>

            <View style={styles.quickActionsGrid}>
              {/* Pay Bills */}
              <AnimatedCard
                style={styles.quickActionItem}
                onPress={() => navigateToSection('PayBillsScreen')}
                animate={!loading}
                from="bottom"
                delay={400}
              >
                <View style={[styles.quickActionIconContainer, { backgroundColor: COLORS.primaryGreen }]}>
                  <Icon name="cash-multiple" size={28} color={COLORS.white} />
                </View>
                <Text style={styles.quickActionText}>Pay Bills</Text>
                <View style={styles.actionArrow}>
                  <Icon name="chevron-right" size={16} color={COLORS.primaryGreen} />
                </View>
              </AnimatedCard>

              {/* Service Alerts */}
              <AnimatedCard
                style={styles.quickActionItem}
                onPress={() => navigateToSection('AlertsScreen')}
                animate={!loading}
                from="bottom"
                delay={500}
              >
                <View style={[styles.quickActionIconContainer, { backgroundColor: COLORS.warning }]}>
                  <Icon name="bell-alert" size={28} color={COLORS.white} />
                </View>
                <Text style={styles.quickActionText}>Service Alerts</Text>
                <View style={styles.actionArrow}>
                  <Icon name="chevron-right" size={16} color={COLORS.warning} />
                </View>
              </AnimatedCard>

              {/* Health Services */}
              <AnimatedCard
                style={styles.quickActionItem}
                onPress={() => navigateToSection('HealthServicesScreen')}
                animate={!loading}
                from="bottom"
                delay={550}
              >
                <View style={[styles.quickActionIconContainer, { backgroundColor: COLORS.success }]}>
                  <Icon name="hospital-box" size={28} color={COLORS.white} />
                </View>
                <Text style={styles.quickActionText}>Health Services</Text>
                <View style={styles.actionArrow}>
                  <Icon name="chevron-right" size={16} color={COLORS.success} />
                </View>
              </AnimatedCard>

              {/* Community Hub */}
              <AnimatedCard
                style={styles.quickActionItem}
                onPress={() => navigateToSection('CommunityHubScreen')}
                animate={!loading}
                from="bottom"
                delay={600}
              >
                <View style={[styles.quickActionIconContainer, { backgroundColor: COLORS.info }]}>
                  <Icon name="account-group" size={28} color={COLORS.white} />
                </View>
                <Text style={styles.quickActionText}>Community Hub</Text>
                <View style={styles.actionArrow}>
                  <Icon name="chevron-right" size={16} color={COLORS.info} />
                </View>
              </AnimatedCard>

              {/* My Account */}
              <AnimatedCard
                style={styles.quickActionItem}
                onPress={() => navigateToSection('MyAccountScreen')}
                animate={!loading}
                from="bottom"
                delay={700}
              >
                <View style={[styles.quickActionIconContainer, { backgroundColor: COLORS.primaryDarkGreen }]}>
                  <Icon name="account-circle" size={28} color={COLORS.white} />
                </View>
                <Text style={styles.quickActionText}>My Account</Text>
                <View style={styles.actionArrow}>
                  <Icon name="chevron-right" size={16} color={COLORS.primaryDarkGreen} />
                </View>
              </AnimatedCard>
            </View>
          </AnimatedCard>
        )}

        {/* Service Status with Enhanced Styling */}
        {renderServiceStatus()}

        {/* AI Assistant Card */}
        <TouchableOpacity
          style={styles.aiAssistantCard}
          onPress={() => navigateToSection('ThusongAIScreen')}
        >
          <View style={styles.aiAssistantContent}>
            <View style={styles.aiIconContainer}>
              <Icon name="robot" size={32} color={COLORS.white} />
            </View>
            <View style={styles.aiTextContainer}>
              <Text style={styles.aiTitle}>Thusong AI Assistant</Text>
              <Text style={styles.aiDescription}>
                Need help? Chat with our AI assistant for guidance on services and more.
              </Text>
            </View>
          </View>
          <Icon name="chevron-right" size={24} color={COLORS.white} />
        </TouchableOpacity>

        {/* Call Presidential Hotline Button */}
        <CustomButton
          title="Call Presidential Hotline"
          onPress={() => navigation.navigate('Call')}
          style={styles.callButton}
          icon={<Icon name="phone" size={20} color={COLORS.white} />}
        />

        {/* Pay Bills Now Button */}
        <CustomButton
          title="Pay Bills Now"
          onPress={() => navigateToSection('PayBillsScreen')}
          style={styles.payBillsButton}
          icon={<Icon name="credit-card-outline" size={20} color={COLORS.white} />}
        />

        {/* Recent Alerts with Enhanced Styling */}
        {loading ? renderSkeletonCard() : (
          <AnimatedCard
            style={styles.cardContainer}
            animate={!loading && dataLoaded.alerts}
            from="left"
            delay={1000}
          >
            <View style={styles.alertsHeader}>
              <View style={styles.sectionTitleContainer}>
                <Icon name="bell-ring" size={20} color={COLORS.primaryGreen} />
                <Text style={styles.sectionTitle}>Recent Alerts</Text>
              </View>
              <TouchableOpacity
                style={styles.viewAllButton}
                onPress={() => navigateToSection('AlertsScreen')}
              >
                <Text style={styles.viewAllText}>View All</Text>
                <Icon name="chevron-right" size={16} color={COLORS.primaryGreen} />
              </TouchableOpacity>
            </View>

            {RECENT_ALERTS.length > 0 ? (
              <FlatList
                data={RECENT_ALERTS}
                renderItem={({ item, index }) => {
                  const itemDelay = 1100 + (index * 100);
                  return (
                    <Animated.View
                      style={{
                        opacity: dataLoaded.alerts ? 1 : 0,
                        transform: [{
                          translateY: dataLoaded.alerts ? 0 : 20
                        }],
                      }}
                    >
                      {renderAlertItem({ item })}
                    </Animated.View>
                  );
                }}
                keyExtractor={item => item.id}
                scrollEnabled={false}
                style={styles.alertsList}
              />
            ) : (
              <View style={styles.noAlertsContainer}>
                <Icon name="bell-off" size={40} color={COLORS.lightGray} />
                <Text style={styles.noAlertsText}>No recent alerts</Text>
              </View>
            )}
          </AnimatedCard>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

// --- Updated Styles ---
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  screenHeader: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    overflow: 'hidden',
    height: 120, // Fixed height instead of animated height
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  screenHeaderTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.white,
    opacity: 0.8,
    marginTop: 4,
  },
  weatherWidgetContainer: {
    marginBottom: SPACING.md,
    padding: 0,
    overflow: 'hidden',
  },
  statusInfoButton: {
    marginLeft: 'auto',
    padding: 4,
  },
  scrollViewContent: {
    padding: SPACING.md,
    paddingBottom: SPACING.xxl, // Extra padding at bottom for tab bar
  },
  // Reusable card container style
  cardContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadows applied dynamically using shadowStyle object
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 15,
    // Shadows applied dynamically
  },
  userInfo: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  welcomeText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 4,
  },
  idText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
    letterSpacing: 0.5,
    marginBottom: 6,
  },
  lastLoginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  lastLoginText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginLeft: 4,
  },
  logoutButton: {
    padding: SPACING.xs,
  },

  // Section title with icon
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginLeft: 8,
  },

  // Enhanced Quick Actions
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    width: '48%',
    backgroundColor: COLORS.white,
    borderRadius: 15,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    marginBottom: SPACING.md,
    alignItems: 'center',
    position: 'relative',
  },
  actionCardShadow: {
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  quickActionIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  quickActionText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
  },
  actionArrow: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },

  // Service Status Styles
  serviceStatusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  serviceStatusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    marginBottom: SPACING.sm,
    backgroundColor: COLORS.offWhite,
    padding: SPACING.sm,
    borderRadius: 10,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: SPACING.sm,
  },
  statusNormal: { backgroundColor: COLORS.success },
  statusWarning: { backgroundColor: COLORS.warning },
  statusIssue: { backgroundColor: COLORS.error },
  serviceStatusText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    fontWeight: FONT_WEIGHTS.medium,
  },

  // AI Assistant Card
  aiAssistantCard: {
    backgroundColor: COLORS.primaryDarkGreen,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  aiAssistantContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  aiIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  aiTextContainer: {
    flex: 1,
  },
  aiTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    marginBottom: 4,
  },
  aiDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    opacity: 0.9,
  },

  // Call Button
  callButton: {
    marginBottom: SPACING.md,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    backgroundColor: '#E53935', // Red color for call button
  },

  // Pay Bills Button
  payBillsButton: {
    marginBottom: SPACING.md,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },

  // Enhanced Alerts
  alertsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
    fontSize: FONT_SIZES.sm,
    marginRight: 4,
  },
  alertsList: {
    marginTop: SPACING.xs,
  },
  alertItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.xs,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    position: 'relative',
  },
  unreadAlert: {
    backgroundColor: COLORS.offWhite,
  },
  alertIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  alertContent: {
    flex: 1,
  },
  alertTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  alertMessage: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: 4,
  },
  alertDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primaryGreen,
    position: 'absolute',
    top: SPACING.sm + 2,
    right: SPACING.sm,
  },

  // No alerts state
  noAlertsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.lg,
  },
  noAlertsText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginTop: SPACING.sm,
  },
});

export default DashboardScreen;