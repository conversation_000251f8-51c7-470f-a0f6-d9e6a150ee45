/**
 * @format
 */

import {AppRegistry} from 'react-native';
import {LogBox} from 'react-native';
import {enableScreens} from 'react-native-screens';
import App from './App';
import {name as appName} from './app.json';

// Disable yellow box warnings in development
LogBox.ignoreLogs([
  'Require cycle:',
  'Non-serializable values were found in the navigation state',
  'VirtualizedLists should never be nested',
]);

// Configure react-native-screens
enableScreens(true);

// Register the app
AppRegistry.registerComponent(appName, () => App);
