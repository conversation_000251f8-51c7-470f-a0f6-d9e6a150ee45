import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
  Modal,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { useMockAuth } from '../../context/MockAuthContext';
import {
  MOCK_PATIENT_RECORDS,
  MOCK_PRESCRIPTIONS,
  createPrescription,
  sendPrescriptionReminder,
  initializeMockDoctorData,
} from '../../data/mockDoctorData';
import AsyncStorage from '@react-native-async-storage/async-storage';

const DoctorPrescriptionScreen = ({ navigation, route }) => {
  const { currentUser } = useMockAuth();
  const initialPatientId = route.params?.patientId;
  const [loading, setLoading] = useState(false);
  const [prescriptions, setPrescriptions] = useState([]);
  const [showNewPrescriptionModal, setShowNewPrescriptionModal] = useState(false);
  const [showPatientSelectModal, setShowPatientSelectModal] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [medications, setMedications] = useState([{
    id: Date.now().toString(),
    name: '',
    dosage: '',
    frequency: '',
    duration: '',
    instructions: '',
  }]);
  const [diagnosis, setDiagnosis] = useState('');
  const [notes, setNotes] = useState('');
  const [activeTab, setActiveTab] = useState('active');

  // Load prescriptions on component mount
  useEffect(() => {
    initializeAndLoadData();
  }, [initialPatientId]);

  // Initialize data and load prescriptions
  const initializeAndLoadData = async () => {
    try {
      // Ensure mock data is initialized
      await initializeMockDoctorData();

      // Load prescriptions
      await loadPrescriptions();

      // If patient ID is provided, set the selected patient
      if (initialPatientId) {
        const patient = MOCK_PATIENT_RECORDS.find(p => p.id === initialPatientId);
        if (patient) {
          setSelectedPatient(patient);
        }
      }
    } catch (error) {
      console.error('Error initializing prescription data:', error);
    }
  };

  // Load prescriptions
  const loadPrescriptions = async () => {
    try {
      // Try to get prescriptions from AsyncStorage first
      const prescriptionsJson = await AsyncStorage.getItem('@doctor_prescriptions');
      let allPrescriptions = prescriptionsJson ? JSON.parse(prescriptionsJson) : [];

      // If no data in storage, fall back to mock data
      if (allPrescriptions.length === 0) {
        allPrescriptions = MOCK_PRESCRIPTIONS;
      }

      // Filter prescriptions by doctor
      const doctorPrescriptions = allPrescriptions.filter(
        prescription => prescription.doctorId === currentUser?.id
      );

      setPrescriptions(doctorPrescriptions);
    } catch (error) {
      console.error('Error loading prescriptions:', error);
      // Fallback to mock data
      const doctorPrescriptions = MOCK_PRESCRIPTIONS.filter(
        prescription => prescription.doctorId === currentUser?.id
      );
      setPrescriptions(doctorPrescriptions);
    }
  };

  // Handle add medication
  const handleAddMedication = () => {
    setMedications([
      ...medications,
      {
        id: Date.now().toString(),
        name: '',
        dosage: '',
        frequency: '',
        duration: '',
        instructions: '',
      }
    ]);
  };

  // Handle remove medication
  const handleRemoveMedication = (id) => {
    if (medications.length === 1) {
      Alert.alert('Error', 'At least one medication is required.');
      return;
    }

    setMedications(medications.filter(med => med.id !== id));
  };

  // Handle medication field change
  const handleMedicationChange = (id, field, value) => {
    setMedications(medications.map(med =>
      med.id === id ? { ...med, [field]: value } : med
    ));
  };

  // Handle create prescription
  const handleCreatePrescription = async () => {
    // Validate form
    if (!selectedPatient) {
      Alert.alert('Error', 'Please select a patient.');
      return;
    }

    if (!diagnosis.trim()) {
      Alert.alert('Error', 'Please enter a diagnosis.');
      return;
    }

    const isValid = medications.every(med =>
      med.name && med.dosage && med.frequency && med.duration
    );

    if (!isValid) {
      Alert.alert('Error', 'Please fill in all medication fields.');
      return;
    }

    setLoading(true);

    try {
      // Format medications (remove id field)
      const formattedMedications = medications.map(({ id, ...rest }) => rest);

      // Create prescription data
      const prescriptionData = {
        patientId: selectedPatient.id,
        patientName: selectedPatient.name,
        doctorId: currentUser.id,
        doctorName: currentUser.name,
        diagnosis: diagnosis.trim(),
        medications: formattedMedications,
        notes: notes.trim(),
        expiryDate: calculateExpiryDate(),
        pharmacy: 'Central City Pharmacy', // Default pharmacy
      };

      // Create prescription
      const result = await createPrescription(prescriptionData);

      if (result) {
        Alert.alert(
          'Success',
          'Prescription created successfully.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Reset form and close modal
                setMedications([{
                  id: Date.now().toString(),
                  name: '',
                  dosage: '',
                  frequency: '',
                  duration: '',
                  instructions: '',
                }]);
                setDiagnosis('');
                setNotes('');
                setShowNewPrescriptionModal(false);

                // Reload prescriptions
                loadPrescriptions();
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to create prescription. Please try again.');
      }
    } catch (error) {
      console.error('Error creating prescription:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle send reminder
  const handleSendReminder = async (prescription) => {
    try {
      const result = await sendPrescriptionReminder(prescription.id);
      if (result) {
        Alert.alert(
          'Reminder Sent',
          `A reminder has been sent to ${prescription.patientName} to collect their prescription.`
        );
        loadPrescriptions();
      } else {
        Alert.alert('Error', 'Failed to send reminder. Please try again.');
      }
    } catch (error) {
      console.error('Error sending reminder:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  // Calculate expiry date (3 months from now)
  const calculateExpiryDate = () => {
    const date = new Date();
    date.setMonth(date.getMonth() + 3);
    return date.toISOString().split('T')[0];
  };

  // Filter prescriptions based on active tab
  const filteredPrescriptions = prescriptions.filter(prescription => {
    if (activeTab === 'active') {
      return prescription.status === 'active';
    } else {
      return prescription.status !== 'active';
    }
  });

  // Render prescription item
  const renderPrescriptionItem = ({ item }) => (
    <View style={styles.prescriptionItem}>
      <View style={styles.prescriptionHeader}>
        <View>
          <Text style={styles.patientName}>{item.patientName}</Text>
          <Text style={styles.prescriptionDate}>{item.date}</Text>
        </View>
        <View style={[
          styles.prescriptionStatus,
          item.dispensed ? styles.dispensedStatus : styles.pendingStatus
        ]}>
          <Text style={styles.prescriptionStatusText}>
            {item.dispensed ? 'Dispensed' : 'Pending'}
          </Text>
        </View>
      </View>

      <View style={styles.medicationsContainer}>
        {item.medications.map((medication, index) => (
          <View key={index} style={styles.medicationItem}>
            <Text style={styles.medicationName}>
              {medication.name} {medication.dosage}
            </Text>
            <Text style={styles.medicationDetails}>
              {medication.frequency} for {medication.duration}
            </Text>
            {medication.instructions && (
              <Text style={styles.medicationInstructions}>
                {medication.instructions}
              </Text>
            )}
          </View>
        ))}
      </View>

      <View style={styles.prescriptionActions}>
        {!item.dispensed && (
          <TouchableOpacity
            style={[
              styles.reminderButton,
              item.collectionReminded && styles.disabledButton
            ]}
            onPress={() => handleSendReminder(item)}
            disabled={item.collectionReminded}
          >
            <Icon
              name="bell-ring-outline"
              size={16}
              color={item.collectionReminded ? COLORS.gray : COLORS.white}
            />
            <Text style={styles.buttonText}>
              {item.collectionReminded ? 'Reminder Sent' : 'Send Reminder'}
            </Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={styles.viewButton}
          onPress={() => navigation.navigate('DoctorPatientDetails', { patientId: item.patientId })}
        >
          <Icon name="account" size={16} color={COLORS.white} />
          <Text style={styles.buttonText}>View Patient</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render patient item for selection
  const renderPatientItem = ({ item }) => (
    <TouchableOpacity
      style={styles.patientItem}
      onPress={() => {
        setSelectedPatient(item);
        setShowPatientSelectModal(false);
      }}
    >
      <View style={styles.patientAvatar}>
        <Text style={styles.avatarText}>
          {item.name.split(' ').map(n => n[0]).join('')}
        </Text>
      </View>
      <View style={styles.patientItemDetails}>
        <Text style={styles.patientItemName}>{item.name}</Text>
        <Text style={styles.patientItemInfo}>
          ID: {item.idNumber} • {item.gender}, {new Date().getFullYear() - new Date(item.dateOfBirth).getFullYear()} years
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Prescriptions</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            if (initialPatientId) {
              setShowNewPrescriptionModal(true);
            } else {
              setShowPatientSelectModal(true);
            }
          }}
        >
          <Icon name="plus" size={24} color={COLORS.primaryGreen} />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'active' && styles.activeTab]}
          onPress={() => setActiveTab('active')}
        >
          <Text style={[styles.tabText, activeTab === 'active' && styles.activeTabText]}>
            Active
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => setActiveTab('history')}
        >
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
            History
          </Text>
        </TouchableOpacity>
      </View>

      {/* Prescriptions List */}
      <FlatList
        data={filteredPrescriptions}
        renderItem={renderPrescriptionItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="pill-off" size={64} color={COLORS.lightGray} />
            <Text style={styles.emptyText}>No prescriptions found</Text>
            <Text style={styles.emptySubtext}>
              {activeTab === 'active'
                ? 'You have no active prescriptions.'
                : 'You have no prescription history.'}
            </Text>
          </View>
        }
      />

      {/* Patient Selection Modal */}
      <Modal
        visible={showPatientSelectModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPatientSelectModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Patient</Text>
              <TouchableOpacity onPress={() => setShowPatientSelectModal(false)}>
                <Icon name="close" size={24} color={COLORS.black} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={MOCK_PATIENT_RECORDS}
              renderItem={renderPatientItem}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.patientList}
            />
          </View>
        </View>
      </Modal>

      {/* New Prescription Modal */}
      <Modal
        visible={showNewPrescriptionModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowNewPrescriptionModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, styles.prescriptionModalContainer]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>New Prescription</Text>
              <TouchableOpacity onPress={() => setShowNewPrescriptionModal(false)}>
                <Icon name="close" size={24} color={COLORS.black} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              {/* Patient Information */}
              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>Patient Information</Text>
                {selectedPatient ? (
                  <View style={styles.selectedPatientContainer}>
                    <Text style={styles.selectedPatientName}>{selectedPatient.name}</Text>
                    <Text style={styles.selectedPatientInfo}>
                      ID: {selectedPatient.idNumber}
                    </Text>
                    <TouchableOpacity
                      style={styles.changePatientButton}
                      onPress={() => setShowPatientSelectModal(true)}
                    >
                      <Text style={styles.changePatientText}>Change Patient</Text>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={styles.selectPatientButton}
                    onPress={() => setShowPatientSelectModal(true)}
                  >
                    <Icon name="account-plus" size={20} color={COLORS.primaryGreen} />
                    <Text style={styles.selectPatientText}>Select Patient</Text>
                  </TouchableOpacity>
                )}
              </View>

              {/* Diagnosis */}
              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>Diagnosis</Text>
                <View style={styles.formRow}>
                  <Text style={styles.formLabel}>Primary Diagnosis:</Text>
                  <TextInput
                    style={styles.formInput}
                    value={diagnosis}
                    onChangeText={setDiagnosis}
                    placeholder="Enter primary diagnosis"
                    multiline={true}
                  />
                </View>
              </View>

              {/* Medications */}
              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>Medications</Text>

                {medications.map((medication, index) => (
                  <View key={medication.id} style={styles.medicationForm}>
                    <View style={styles.medicationFormHeader}>
                      <Text style={styles.medicationFormTitle}>
                        Medication {index + 1}
                      </Text>
                      {medications.length > 1 && (
                        <TouchableOpacity
                          style={styles.removeMedicationButton}
                          onPress={() => handleRemoveMedication(medication.id)}
                        >
                          <Icon name="close" size={20} color={COLORS.error} />
                        </TouchableOpacity>
                      )}
                    </View>

                    <View style={styles.formRow}>
                      <Text style={styles.formLabel}>Medication Name:</Text>
                      <TextInput
                        style={styles.formInput}
                        value={medication.name}
                        onChangeText={(value) => handleMedicationChange(medication.id, 'name', value)}
                        placeholder="Enter medication name"
                      />
                    </View>

                    <View style={styles.formRow}>
                      <Text style={styles.formLabel}>Dosage:</Text>
                      <TextInput
                        style={styles.formInput}
                        value={medication.dosage}
                        onChangeText={(value) => handleMedicationChange(medication.id, 'dosage', value)}
                        placeholder="e.g., 10mg"
                      />
                    </View>

                    <View style={styles.formRow}>
                      <Text style={styles.formLabel}>Frequency:</Text>
                      <TextInput
                        style={styles.formInput}
                        value={medication.frequency}
                        onChangeText={(value) => handleMedicationChange(medication.id, 'frequency', value)}
                        placeholder="e.g., Once daily"
                      />
                    </View>

                    <View style={styles.formRow}>
                      <Text style={styles.formLabel}>Duration:</Text>
                      <TextInput
                        style={styles.formInput}
                        value={medication.duration}
                        onChangeText={(value) => handleMedicationChange(medication.id, 'duration', value)}
                        placeholder="e.g., 2 weeks"
                      />
                    </View>

                    <View style={styles.formRow}>
                      <Text style={styles.formLabel}>Instructions:</Text>
                      <TextInput
                        style={[styles.formInput, styles.instructionsInput]}
                        value={medication.instructions}
                        onChangeText={(value) => handleMedicationChange(medication.id, 'instructions', value)}
                        placeholder="Special instructions"
                        multiline={true}
                      />
                    </View>
                  </View>
                ))}

                <TouchableOpacity
                  style={styles.addMedicationButton}
                  onPress={handleAddMedication}
                >
                  <Icon name="plus" size={20} color={COLORS.primaryGreen} />
                  <Text style={styles.addMedicationText}>Add Another Medication</Text>
                </TouchableOpacity>
              </View>

              {/* Additional Notes */}
              <View style={styles.formSection}>
                <Text style={styles.formSectionTitle}>Additional Notes</Text>
                <View style={styles.formRow}>
                  <Text style={styles.formLabel}>Notes (Optional):</Text>
                  <TextInput
                    style={[styles.formInput, styles.instructionsInput]}
                    value={notes}
                    onChangeText={setNotes}
                    placeholder="Additional notes or instructions for the patient"
                    multiline={true}
                  />
                </View>
              </View>

              {/* Submit Button */}
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleCreatePrescription}
                disabled={loading}
              >
                <Text style={styles.submitButtonText}>
                  {loading ? 'Creating...' : 'Create Prescription'}
                </Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  addButton: {
    padding: SPACING.xs,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.sm,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.primaryGreen,
  },
  tabText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  activeTabText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  listContent: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra space at bottom
  },
  prescriptionItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  prescriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  patientName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  prescriptionDate: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  prescriptionStatus: {
    paddingVertical: 4,
    paddingHorizontal: SPACING.sm,
    borderRadius: 15,
  },
  pendingStatus: {
    backgroundColor: COLORS.warning + '30', // 30% opacity
  },
  dispensedStatus: {
    backgroundColor: COLORS.success + '30', // 30% opacity
  },
  prescriptionStatusText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  medicationsContainer: {
    marginBottom: SPACING.md,
  },
  medicationItem: {
    marginBottom: SPACING.sm,
    paddingLeft: SPACING.sm,
    borderLeftWidth: 2,
    borderLeftColor: COLORS.primaryGreen,
  },
  medicationName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  medicationDetails: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  medicationInstructions: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
    fontStyle: 'italic',
  },
  prescriptionActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  reminderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.warning,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 5,
    marginRight: SPACING.sm,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primaryGreen,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 5,
  },
  disabledButton: {
    backgroundColor: COLORS.lightGray,
  },
  buttonText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
    marginTop: SPACING.xl,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SPACING.md,
    maxHeight: '80%',
  },
  prescriptionModalContainer: {
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  modalTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  modalContent: {
    flex: 1,
  },
  patientList: {
    paddingBottom: SPACING.md,
  },
  patientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  patientAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primaryGreen,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  avatarText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  patientItemDetails: {
    flex: 1,
  },
  patientItemName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  patientItemInfo: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  formSection: {
    marginBottom: SPACING.lg,
  },
  formSectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  selectedPatientContainer: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
  },
  selectedPatientName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  selectedPatientInfo: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.sm,
  },
  changePatientButton: {
    alignSelf: 'flex-start',
  },
  changePatientText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  selectPatientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.offWhite,
    padding: SPACING.md,
    borderRadius: 10,
  },
  selectPatientText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: SPACING.xs,
  },
  medicationForm: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  medicationFormHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  medicationFormTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  removeMedicationButton: {
    padding: SPACING.xs,
  },
  formRow: {
    marginBottom: SPACING.sm,
  },
  formLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  formInput: {
    backgroundColor: COLORS.white,
    borderRadius: 5,
    padding: SPACING.sm,
    fontSize: FONT_SIZES.md,
  },
  instructionsInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  addMedicationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
    borderRadius: 10,
    borderStyle: 'dashed',
  },
  addMedicationText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: SPACING.xs,
  },
  submitButton: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  submitButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
});

export default DoctorPrescriptionScreen;
