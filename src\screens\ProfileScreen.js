import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import { useMockAuth } from '../context/MockAuthContext';

const ProfileScreen = ({ navigation }) => {
  const { currentUser, signOut } = useMockAuth();

  const handleLogout = async () => {
    try {
      const result = await signOut();
      if (result.success) {
        // Explicitly navigate to Login screen
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        });
      } else {
        Alert.alert('Logout Failed', 'Could not log out. Please try again.');
      }
    } catch (error) {
      console.error('Error during logout:', error);
      Alert.alert('Error', 'An unexpected error occurred during logout.');
    }
  };

  const handleEditProfile = () => {
    Alert.alert('Edit Profile', 'This feature is coming soon!');
  };

  const handleChangePassword = () => {
    Alert.alert('Change Password', 'This feature is coming soon!');
  };

  const handleNotificationSettings = () => {
    Alert.alert('Notification Settings', 'This feature is coming soon!');
  };

  const handlePrivacySettings = () => {
    Alert.alert('Privacy Settings', 'This feature is coming soon!');
  };

  const handleHelpSupport = () => {
    Alert.alert('Help & Support', 'This feature is coming soon!');
  };

  const handleAbout = () => {
    Alert.alert('About', 'Let\'s Talk App v1.0.0\n\nDeveloped for citizens to easily access government services.');
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Profile</Text>
        <View style={styles.rightPlaceholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Card */}
        <View style={styles.profileCard}>
          <View style={styles.profileImageContainer}>
            <View style={styles.profileImage}>
              <Text style={styles.profileInitials}>
                {currentUser?.name?.split(' ').map(n => n[0]).join('') || 'U'}
              </Text>
            </View>
          </View>

          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>{currentUser?.name || 'User'}</Text>
            {currentUser?.idNumber && (
              <Text style={styles.profileIdNumber}>
                ID: {currentUser.idNumber.replace(/(\d{6})(\d{4})(\d{3})/, '$1 $2 $3')}
              </Text>
            )}
            {currentUser?.email && (
              <Text style={styles.profileEmail}>{currentUser.email}</Text>
            )}
          </View>

          <TouchableOpacity
            style={styles.editProfileButton}
            onPress={handleEditProfile}
          >
            <Text style={styles.editProfileButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>

        {/* Settings Section */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Account Settings</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleChangePassword}
          >
            <Icon name="lock-outline" size={24} color={COLORS.primaryGreen} />
            <Text style={styles.settingText}>Change Password</Text>
            <Icon name="chevron-right" size={24} color={COLORS.darkGray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleNotificationSettings}
          >
            <Icon name="bell-outline" size={24} color={COLORS.primaryGreen} />
            <Text style={styles.settingText}>Notification Settings</Text>
            <Icon name="chevron-right" size={24} color={COLORS.darkGray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handlePrivacySettings}
          >
            <Icon name="shield-outline" size={24} color={COLORS.primaryGreen} />
            <Text style={styles.settingText}>Privacy Settings</Text>
            <Icon name="chevron-right" size={24} color={COLORS.darkGray} />
          </TouchableOpacity>
        </View>

        {/* Support Section */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Support</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleHelpSupport}
          >
            <Icon name="help-circle-outline" size={24} color={COLORS.primaryGreen} />
            <Text style={styles.settingText}>Help & Support</Text>
            <Icon name="chevron-right" size={24} color={COLORS.darkGray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleAbout}
          >
            <Icon name="information-outline" size={24} color={COLORS.primaryGreen} />
            <Text style={styles.settingText}>About</Text>
            <Icon name="chevron-right" size={24} color={COLORS.darkGray} />
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Icon name="logout" size={24} color={COLORS.white} />
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  rightPlaceholder: {
    width: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra padding for bottom tab bar
  },
  profileCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileImageContainer: {
    marginBottom: SPACING.md,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.primaryGreen,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInitials: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  profileInfo: {
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  profileName: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  profileIdNumber: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  profileEmail: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  editProfileButton: {
    backgroundColor: COLORS.offWhite,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
  },
  editProfileButtonText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
  },
  settingsSection: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.xs,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  settingText: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    marginLeft: SPACING.md,
  },
  logoutButton: {
    backgroundColor: COLORS.error,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    borderRadius: 10,
    marginTop: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoutButtonText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
    fontSize: FONT_SIZES.md,
    marginLeft: SPACING.sm,
  },
});

export default ProfileScreen;
