import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from 'react-native';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomInput from '../components/CustomInput';
import CustomButton from '../components/CustomButton';
import { useMockAuth } from '../context/MockAuthContext';
import { IMAGES } from '../assets/images';

const ConfirmationScreen = ({ navigation, route }) => {
  const [confirmationCode, setConfirmationCode] = useState('');
  const [codeError, setCodeError] = useState('');
  const [resendLoading, setResendLoading] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);

  const { confirmSignUp, loading, authError, clearError, resendVerificationCode } = useMockAuth();
  const { idNumber } = route.params || {};

  const validateCode = (code) => {
    if (!code) {
      setCodeError('Confirmation code is required');
      return false;
    } else if (code.length < 6) {
      setCodeError('Please enter a valid confirmation code');
      return false;
    }
    setCodeError('');
    return true;
  };

  const handleConfirm = async () => {
    const isCodeValid = validateCode(confirmationCode);

    if (isCodeValid && idNumber) {
      const result = await confirmSignUp(idNumber, confirmationCode);
      if (result.success) {
        alert('Account confirmed successfully! Please log in.');
        navigation.navigate('Login');
      }
    } else if (!idNumber) {
      alert('ID Number is missing. Please go back and try again.');
    }
  };

  const handleResendCode = async () => {
    if (!idNumber) {
      alert('Unable to resend code. Please try again later.');
      return;
    }

    setResendLoading(true);
    const result = await resendVerificationCode(idNumber);
    setResendLoading(false);

    if (result.success) {
      setResendSuccess(true);
      alert('A new verification code has been sent to your email.');

      // Reset the success message after a few seconds
      setTimeout(() => {
        setResendSuccess(false);
      }, 5000);
    } else {
      alert('Failed to resend verification code. Please try again.');
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.appName}>Let's Talk</Text>
            <Text style={styles.tagline}>Verify your account</Text>
          </View>

          <View style={styles.logoContainer}>
            <Image
              source={IMAGES.logo}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.welcomeText}>Confirm Your Email</Text>
            <Text style={styles.confirmText}>
              We've sent a confirmation code to your email. Please enter it below to verify your account.
            </Text>

            {authError && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{authError}</Text>
                <TouchableOpacity onPress={clearError}>
                  <Text style={styles.dismissText}>Dismiss</Text>
                </TouchableOpacity>
              </View>
            )}

            <CustomInput
              label="Confirmation Code"
              placeholder="Enter the 6-digit code"
              value={confirmationCode}
              onChangeText={(text) => {
                setConfirmationCode(text);
                if (codeError) validateCode(text);
              }}
              keyboardType="number-pad"
              error={codeError}
              maxLength={6}
            />

            <CustomButton
              title="Confirm Account"
              onPress={handleConfirm}
              loading={loading}
              style={styles.confirmButton}
            />

            <TouchableOpacity
              onPress={handleResendCode}
              style={styles.resendContainer}
              disabled={resendLoading}
            >
              <Text style={styles.resendText}>
                {resendLoading
                  ? 'Sending...'
                  : resendSuccess
                    ? 'Code sent!'
                    : "Didn't receive a code? Resend"}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={handleBack} style={styles.backContainer}>
              <Text style={styles.backText}>Back to Login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: SPACING.xl,
  },
  header: {
    alignItems: 'center',
    marginTop: SPACING.xl,
    marginBottom: SPACING.xl,
  },
  appName: {
    fontSize: FONT_SIZES.title,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.xs,
  },
  tagline: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  logoContainer: {
    marginVertical: SPACING.md,
    alignItems: 'center',
  },
  logo: {
    width: 150,
    height: 150,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  formContainer: {
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  welcomeText: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  confirmText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.xl,
    textAlign: 'center',
    paddingHorizontal: SPACING.md,
  },
  errorContainer: {
    width: '100%',
    backgroundColor: '#FFEBEE',
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: FONT_SIZES.sm,
    flex: 1,
  },
  dismissText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
    fontSize: FONT_SIZES.sm,
  },
  confirmButton: {
    marginTop: SPACING.lg,
  },
  resendContainer: {
    marginTop: SPACING.lg,
    padding: SPACING.sm,
  },
  resendText: {
    color: COLORS.primaryGreen,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
  },
  backContainer: {
    marginTop: SPACING.md,
    padding: SPACING.sm,
  },
  backText: {
    color: COLORS.darkGray,
    fontSize: FONT_SIZES.sm,
  },
});

export default ConfirmationScreen;
