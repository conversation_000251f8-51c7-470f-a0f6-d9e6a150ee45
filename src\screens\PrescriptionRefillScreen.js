import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';
import GradientBackground from '../components/GradientBackground';
import { useMockAuth } from '../context/MockAuthContext';

const PrescriptionRefillScreen = ({ navigation }) => {
  const { currentUser } = useMockAuth();
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedPrescription, setSelectedPrescription] = useState(null);
  const [showRefillModal, setShowRefillModal] = useState(false);
  const [refillRequests, setRefillRequests] = useState([]);

  // Mock prescription data for the current user
  const mockUserPrescriptions = [
    {
      id: 'rx-1',
      medicationName: 'Lisinopril',
      dosage: '10mg',
      frequency: 'Once daily',
      prescribedBy: 'Dr. Sarah Johnson',
      prescribedDate: '2024-01-15',
      expiryDate: '2024-07-15',
      refillsRemaining: 2,
      totalRefills: 3,
      lastRefillDate: '2024-02-15',
      pharmacy: 'Central City Pharmacy',
      status: 'active',
      instructions: 'Take in the morning with food',
      quantity: '30 tablets',
      diagnosis: 'Hypertension',
    },
    {
      id: 'rx-2',
      medicationName: 'Metformin',
      dosage: '500mg',
      frequency: 'Twice daily',
      prescribedBy: 'Dr. Michael Ndlovu',
      prescribedDate: '2024-02-01',
      expiryDate: '2024-08-01',
      refillsRemaining: 1,
      totalRefills: 2,
      lastRefillDate: '2024-03-01',
      pharmacy: 'Sunshine Pharmacy',
      status: 'active',
      instructions: 'Take with meals',
      quantity: '60 tablets',
      diagnosis: 'Type 2 Diabetes',
    },
    {
      id: 'rx-3',
      medicationName: 'Atorvastatin',
      dosage: '20mg',
      frequency: 'Once daily',
      prescribedBy: 'Dr. Sarah Johnson',
      prescribedDate: '2024-01-10',
      expiryDate: '2024-07-10',
      refillsRemaining: 0,
      totalRefills: 2,
      lastRefillDate: '2024-04-10',
      pharmacy: 'Central City Pharmacy',
      status: 'expired',
      instructions: 'Take in the evening',
      quantity: '30 tablets',
      diagnosis: 'High Cholesterol',
    },
  ];

  useEffect(() => {
    loadPrescriptions();
  }, []);

  const loadPrescriptions = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setPrescriptions(mockUserPrescriptions);
      setLoading(false);
    }, 1000);
  };

  const handleRefillRequest = (prescription) => {
    if (prescription.refillsRemaining === 0) {
      Alert.alert(
        'No Refills Remaining',
        'This prescription has no refills remaining. Please contact your doctor for a new prescription.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Contact Doctor', onPress: () => navigation.navigate('BookingScreen') }
        ]
      );
      return;
    }

    setSelectedPrescription(prescription);
    setShowRefillModal(true);
  };

  const confirmRefillRequest = () => {
    if (!selectedPrescription) return;

    const newRefillRequest = {
      id: `refill-${Date.now()}`,
      prescriptionId: selectedPrescription.id,
      medicationName: selectedPrescription.medicationName,
      dosage: selectedPrescription.dosage,
      quantity: selectedPrescription.quantity,
      pharmacy: selectedPrescription.pharmacy,
      requestDate: new Date().toLocaleDateString(),
      status: 'pending',
      estimatedReadyDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleDateString(), // Tomorrow
    };

    setRefillRequests([...refillRequests, newRefillRequest]);
    
    // Update prescription refills remaining
    const updatedPrescriptions = prescriptions.map(rx => 
      rx.id === selectedPrescription.id 
        ? { ...rx, refillsRemaining: rx.refillsRemaining - 1 }
        : rx
    );
    setPrescriptions(updatedPrescriptions);

    setShowRefillModal(false);
    setSelectedPrescription(null);

    Alert.alert(
      'Refill Requested',
      `Your refill request for ${selectedPrescription.medicationName} has been submitted to ${selectedPrescription.pharmacy}. You will be notified when it's ready for pickup.`,
      [{ text: 'OK' }]
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return COLORS.primaryGreen;
      case 'expired': return COLORS.red;
      case 'pending': return COLORS.orange;
      default: return COLORS.gray;
    }
  };

  const renderPrescriptionItem = ({ item }) => (
    <View style={styles.prescriptionCard}>
      <View style={styles.prescriptionHeader}>
        <View style={styles.medicationInfo}>
          <Text style={styles.medicationName}>{item.medicationName}</Text>
          <Text style={styles.dosageText}>{item.dosage} - {item.frequency}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status.toUpperCase()}</Text>
        </View>
      </View>

      <View style={styles.prescriptionDetails}>
        <View style={styles.detailRow}>
          <Icon name="doctor" size={16} color={COLORS.gray} />
          <Text style={styles.detailText}>Prescribed by {item.prescribedBy}</Text>
        </View>
        <View style={styles.detailRow}>
          <Icon name="calendar" size={16} color={COLORS.gray} />
          <Text style={styles.detailText}>Prescribed on {item.prescribedDate}</Text>
        </View>
        <View style={styles.detailRow}>
          <Icon name="store" size={16} color={COLORS.gray} />
          <Text style={styles.detailText}>{item.pharmacy}</Text>
        </View>
        <View style={styles.detailRow}>
          <Icon name="refresh" size={16} color={COLORS.gray} />
          <Text style={styles.detailText}>
            {item.refillsRemaining} of {item.totalRefills} refills remaining
          </Text>
        </View>
      </View>

      <View style={styles.prescriptionActions}>
        <TouchableOpacity
          style={[
            styles.refillButton,
            item.status !== 'active' && styles.disabledButton
          ]}
          onPress={() => handleRefillRequest(item)}
          disabled={item.status !== 'active'}
        >
          <Icon name="refresh" size={16} color={COLORS.white} />
          <Text style={styles.buttonText}>Request Refill</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.detailsButton}
          onPress={() => {/* Navigate to prescription details */}}
        >
          <Icon name="information" size={16} color={COLORS.primaryGreen} />
          <Text style={styles.detailsButtonText}>Details</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderRefillModal = () => (
    <Modal
      visible={showRefillModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowRefillModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <GradientBackground
            colors={[COLORS.primaryGreen, COLORS.primaryLightGreen]}
            style={styles.modalHeader}
          >
            <Text style={styles.modalTitle}>Confirm Refill Request</Text>
            <TouchableOpacity
              onPress={() => setShowRefillModal(false)}
              style={styles.closeButton}
            >
              <Icon name="close" size={24} color={COLORS.white} />
            </TouchableOpacity>
          </GradientBackground>

          <View style={styles.modalContent}>
            {selectedPrescription && (
              <>
                <Text style={styles.confirmText}>
                  You are requesting a refill for:
                </Text>
                
                <View style={styles.refillSummary}>
                  <Text style={styles.refillMedication}>
                    {selectedPrescription.medicationName} {selectedPrescription.dosage}
                  </Text>
                  <Text style={styles.refillQuantity}>
                    Quantity: {selectedPrescription.quantity}
                  </Text>
                  <Text style={styles.refillPharmacy}>
                    Pharmacy: {selectedPrescription.pharmacy}
                  </Text>
                  <Text style={styles.refillRemaining}>
                    Refills remaining after this request: {selectedPrescription.refillsRemaining - 1}
                  </Text>
                </View>

                <Text style={styles.estimateText}>
                  Estimated ready time: 24 hours
                </Text>

                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowRefillModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={confirmRefillRequest}
                  >
                    <Text style={styles.confirmButtonText}>Confirm Request</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Prescription Refills</Text>
        <TouchableOpacity
          style={styles.historyButton}
          onPress={() => {/* Navigate to refill history */}}
        >
          <Icon name="history" size={24} color={COLORS.primaryGreen} />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={{ flexGrow: 1, paddingBottom: SPACING.lg }}
        showsVerticalScrollIndicator={false}
      >
        {/* Info Card */}
        <View style={styles.infoCard}>
          <Icon name="information" size={24} color={COLORS.primaryGreen} />
          <View style={styles.infoText}>
            <Text style={styles.infoTitle}>How Prescription Refills Work</Text>
            <Text style={styles.infoDescription}>
              Request refills for your active prescriptions. You'll be notified when they're ready for pickup at your pharmacy.
            </Text>
          </View>
        </View>

        {/* Active Prescriptions */}
        <Text style={styles.sectionTitle}>Your Active Prescriptions</Text>

        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading prescriptions...</Text>
          </View>
        ) : (
          <FlatList
            data={prescriptions}
            renderItem={renderPrescriptionItem}
            keyExtractor={item => item.id}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Icon name="pill-off" size={64} color={COLORS.lightGray} />
                <Text style={styles.emptyText}>No Active Prescriptions</Text>
                <Text style={styles.emptySubtext}>
                  You don't have any active prescriptions that can be refilled.
                </Text>
                <CustomButton
                  title="Book Appointment"
                  onPress={() => navigation.navigate('BookingScreen')}
                  style={styles.bookButton}
                />
              </View>
            }
          />
        )}

        {/* Recent Refill Requests */}
        {refillRequests.length > 0 && (
          <>
            <Text style={styles.sectionTitle}>Recent Refill Requests</Text>
            {refillRequests.map((request) => (
              <View key={request.id} style={styles.refillRequestCard}>
                <View style={styles.requestHeader}>
                  <Text style={styles.requestMedication}>{request.medicationName}</Text>
                  <View style={[styles.requestStatus, { backgroundColor: COLORS.orange }]}>
                    <Text style={styles.requestStatusText}>PENDING</Text>
                  </View>
                </View>
                <Text style={styles.requestDate}>Requested on {request.requestDate}</Text>
                <Text style={styles.requestReady}>
                  Estimated ready: {request.estimatedReadyDate}
                </Text>
              </View>
            ))}
          </>
        )}
      </ScrollView>

      {renderRefillModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  historyButton: {
    padding: SPACING.xs,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    borderRadius: 12,
    marginBottom: SPACING.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoText: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  infoTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  infoDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
    marginTop: SPACING.md,
  },
  prescriptionCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  prescriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  medicationInfo: {
    flex: 1,
  },
  medicationName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  dosageText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  statusBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  prescriptionDetails: {
    marginBottom: SPACING.md,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  detailText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: SPACING.sm,
  },
  prescriptionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  refillButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primaryGreen,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 8,
    flex: 1,
    marginRight: SPACING.sm,
    justifyContent: 'center',
  },
  disabledButton: {
    backgroundColor: COLORS.gray,
  },
  buttonText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.white,
    marginLeft: SPACING.xs,
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 8,
    justifyContent: 'center',
  },
  detailsButtonText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.primaryGreen,
    marginLeft: SPACING.xs,
  },
  loadingContainer: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
  },
  emptyContainer: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  bookButton: {
    marginTop: SPACING.md,
  },
  refillRequestCard: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.orange,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  requestMedication: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  requestStatus: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 8,
  },
  requestStatusText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  requestDate: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  requestReady: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  modalTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  modalContent: {
    padding: SPACING.lg,
  },
  confirmText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.md,
  },
  refillSummary: {
    backgroundColor: COLORS.offWhite,
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.md,
  },
  refillMedication: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  refillQuantity: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  refillPharmacy: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  refillRemaining: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  estimateText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.orange,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray,
    borderRadius: 8,
    marginRight: SPACING.sm,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.gray,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 8,
    marginLeft: SPACING.sm,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.white,
  },
});

export default PrescriptionRefillScreen;
