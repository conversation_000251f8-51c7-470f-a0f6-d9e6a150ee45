/**
 * Mock data for <PERSON> <PERSON><PERSON> features
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { HEALTHCARE_PROVIDERS } from './mockHealthData';

// Mock doctor users
export const MOCK_DOCTOR_USERS = [
  {
    id: '5',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'doctor',
    specialty: 'General Practitioner',
    location: 'Central City Clinic',
    licenseNumber: 'MD12345',
    profileImage: null,
  },
  {
    id: '6',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'doctor',
    specialty: 'Pediatrician',
    location: "Sunshine Children's Clinic",
    licenseNumber: 'MD67890',
    profileImage: null,
  },
  {
    id: '7',
    name: 'Dr. <PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'doctor',
    specialty: 'Dentist',
    location: 'Bright Smile Dental Clinic',
    licenseNumber: 'DDS54321',
    profileImage: null,
  },
];

// Mock appointments with more details for doctor view
export const MOCK_DOCTOR_APPOINTMENTS = [
  // Today's appointments for Dr. <PERSON> (id: 5)
  {
    id: 'appointment-1',
    patientId: 'patient-1',
    patientName: 'John Smith',
    patientAge: 45,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '09:00 AM',
    duration: 30, // minutes
    reason: 'Annual check-up',
    status: 'completed',
    notes: 'BP: 130/85, Weight: 82kg. Discussed diet and exercise. Patient reports occasional headaches.',
    doctorId: '5',
  },
  {
    id: 'appointment-2',
    patientId: 'patient-2',
    patientName: 'Mary Johnson',
    patientAge: 35,
    patientGender: 'Female',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '10:00 AM',
    duration: 30, // minutes
    reason: 'Prenatal check-up',
    status: 'completed',
    notes: 'Pregnancy progressing well at 24 weeks. Fetal heartbeat normal. Prescribed prenatal vitamins.',
    doctorId: '5',
  },
  {
    id: 'appointment-3',
    patientId: 'patient-6',
    patientName: 'Thabo Mbeki',
    patientAge: 52,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '11:30 AM',
    duration: 45, // minutes
    reason: 'Hypertension follow-up',
    status: 'completed',
    notes: 'BP still elevated at 145/95 despite medication. Increased dosage of Lisinopril. Scheduled follow-up in 2 weeks.',
    doctorId: '5',
  },
  {
    id: 'appointment-4',
    patientId: 'patient-7',
    patientName: 'Nomsa Khumalo',
    patientAge: 29,
    patientGender: 'Female',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '02:00 PM',
    duration: 30, // minutes
    reason: 'Migraine consultation',
    status: 'scheduled',
    notes: 'Recurring migraines, 2-3 times per week. Currently using over-the-counter pain relievers with limited effect.',
    doctorId: '5',
  },
  {
    id: 'appointment-5',
    patientId: 'patient-8',
    patientName: 'Sipho Mthembu',
    patientAge: 41,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '03:00 PM',
    duration: 30, // minutes
    reason: 'Lower back pain',
    status: 'scheduled',
    notes: 'Experiencing lower back pain for 3 weeks. No previous history of back problems. Works in construction.',
    doctorId: '5',
  },
  {
    id: 'appointment-6',
    patientId: 'patient-9',
    patientName: 'Lerato Moloi',
    patientAge: 67,
    patientGender: 'Female',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '04:00 PM',
    duration: 45, // minutes
    reason: 'Diabetes management',
    status: 'scheduled',
    notes: 'Type 2 diabetes, diagnosed 10 years ago. Recent HbA1c: 7.8%. Needs medication review.',
    doctorId: '5',
  },

  // Tomorrow's appointments for Dr. Sarah Johnson (id: 5)
  {
    id: 'appointment-7',
    patientId: 'patient-10',
    patientName: 'Mandla Zulu',
    patientAge: 38,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    time: '09:00 AM',
    duration: 30, // minutes
    reason: 'Asthma follow-up',
    status: 'scheduled',
    notes: 'Asthma well-controlled with current medication. Needs prescription renewal.',
    doctorId: '5',
  },
  {
    id: 'appointment-8',
    patientId: 'patient-11',
    patientName: 'Precious Ndlovu',
    patientAge: 32,
    patientGender: 'Female',
    patientContact: '**********',
    date: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    time: '10:00 AM',
    duration: 30, // minutes
    reason: 'Thyroid check-up',
    status: 'scheduled',
    notes: 'Hypothyroidism, diagnosed 3 years ago. Currently on Levothyroxine 50mcg daily.',
    doctorId: '5',
  },
  {
    id: 'appointment-9',
    patientId: 'patient-12',
    patientName: 'Bongani Nkosi',
    patientAge: 45,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    time: '11:30 AM',
    duration: 45, // minutes
    reason: 'Chest pain investigation',
    status: 'scheduled',
    notes: 'Experiencing intermittent chest pain for the past week. Family history of heart disease.',
    doctorId: '5',
  },

  // Appointments for Dr. Michael Ndlovu (id: 6)
  {
    id: 'appointment-10',
    patientId: 'patient-3',
    patientName: 'David Nkosi',
    patientAge: 8,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '09:30 AM',
    duration: 30, // minutes
    reason: 'Vaccination',
    status: 'completed',
    notes: 'Administered MMR vaccine. No immediate adverse reactions. Provided vaccination certificate.',
    doctorId: '6',
  },
  {
    id: 'appointment-11',
    patientId: 'patient-13',
    patientName: 'Amahle Dlamini',
    patientAge: 4,
    patientGender: 'Female',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '10:30 AM',
    duration: 30, // minutes
    reason: 'Routine check-up',
    status: 'completed',
    notes: 'Growth and development on track. Weight: 18kg, Height: 102cm. No concerns reported by parent.',
    doctorId: '6',
  },
  {
    id: 'appointment-12',
    patientId: 'patient-14',
    patientName: 'Themba Moyo',
    patientAge: 6,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '02:00 PM',
    duration: 30, // minutes
    reason: 'Persistent cough',
    status: 'scheduled',
    notes: 'Coughing for 2 weeks, worse at night. No fever. Previously healthy.',
    doctorId: '6',
  },

  // Appointments for Dr. Thabo Molefe (id: 7)
  {
    id: 'appointment-13',
    patientId: 'patient-5',
    patientName: 'James Mokoena',
    patientAge: 28,
    patientGender: 'Male',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '09:00 AM',
    duration: 30, // minutes
    reason: 'Dental cleaning',
    status: 'completed',
    notes: 'Routine cleaning performed. No cavities. Advised on improved flossing technique.',
    doctorId: '7',
  },
  {
    id: 'appointment-14',
    patientId: 'patient-15',
    patientName: 'Zanele Sithole',
    patientAge: 35,
    patientGender: 'Female',
    patientContact: '**********',
    date: new Date().toISOString(), // Today
    time: '11:00 AM',
    duration: 45, // minutes
    reason: 'Tooth extraction',
    status: 'completed',
    notes: 'Extracted lower right molar (tooth #46). Prescribed antibiotics and pain medication.',
    doctorId: '7',
  },
];

// Mock patient records
export const MOCK_PATIENT_RECORDS = [
  {
    id: 'patient-1',
    name: 'John Smith',
    idNumber: '7501015012087',
    dateOfBirth: '1975-01-01',
    gender: 'Male',
    contactNumber: '**********',
    email: '<EMAIL>',
    address: '123 Main Street, Central City',
    emergencyContact: 'Jane Smith (Wife) - **********',
    medicalAidNumber: 'MA123456',
    medicalAidScheme: 'National Health',
    bloodType: 'O+',
    allergies: ['Penicillin', 'Peanuts'],
    chronicConditions: ['Hypertension'],
    currentMedications: ['Lisinopril 10mg daily'],
    pastVisits: [
      {
        date: '2023-05-15',
        reason: 'Annual check-up',
        diagnosis: 'Hypertension, well controlled',
        treatment: 'Continue current medication',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'BP: 130/85, Weight: 82kg, Height: 178cm. Patient reports occasional headaches.',
        vitals: {
          bloodPressure: '130/85',
          heartRate: '72',
          temperature: '36.6',
          respiratoryRate: '16',
          weight: '82',
          height: '178',
          bmi: '25.9'
        }
      },
      {
        date: '2022-11-20',
        reason: 'Flu symptoms',
        diagnosis: 'Seasonal influenza',
        treatment: 'Symptomatic treatment, rest, fluids',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'Fever, cough, body aches. Negative for COVID-19. Advised to rest and increase fluid intake.',
        vitals: {
          bloodPressure: '125/80',
          heartRate: '88',
          temperature: '38.2',
          respiratoryRate: '18',
          weight: '83',
          height: '178',
          bmi: '26.2'
        }
      },
      {
        date: '2022-06-20',
        reason: 'Headache consultation',
        diagnosis: 'Tension headaches',
        treatment: 'Prescribed Paracetamol, stress management techniques',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'Patient reports frequent headaches, likely stress-related. No neurological concerns.',
        vitals: {
          bloodPressure: '135/88',
          heartRate: '76',
          temperature: '36.5',
          respiratoryRate: '16',
          weight: '84',
          height: '178',
          bmi: '26.5'
        }
      },
    ],
  },
  {
    id: 'patient-2',
    name: 'Mary Johnson',
    idNumber: '8802025012087',
    dateOfBirth: '1988-02-02',
    gender: 'Female',
    contactNumber: '**********',
    email: '<EMAIL>',
    address: '456 Oak Avenue, Westside',
    emergencyContact: 'Robert Johnson (Husband) - **********',
    medicalAidNumber: 'MA654321',
    medicalAidScheme: 'Health Plus',
    bloodType: 'A+',
    allergies: ['Sulfa drugs'],
    chronicConditions: [],
    currentMedications: ['Prenatal vitamins', 'Folic Acid 5mg daily'],
    pastVisits: [
      {
        date: '2023-05-20',
        reason: 'Prenatal check-up',
        diagnosis: 'Pregnancy, 24 weeks, normal progression',
        treatment: 'Continue prenatal vitamins, scheduled follow-up in 4 weeks',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'Fetal heartbeat normal at 140 bpm. No complications. Patient reports mild back pain.',
        vitals: {
          bloodPressure: '118/75',
          heartRate: '82',
          temperature: '36.7',
          respiratoryRate: '18',
          weight: '68',
          height: '165',
          bmi: '25.0'
        }
      },
      {
        date: '2023-03-10',
        reason: 'Pregnancy confirmation',
        diagnosis: 'Pregnancy, 8 weeks',
        treatment: 'Prenatal vitamins, scheduled follow-up',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'First pregnancy. No complications. Prescribed prenatal vitamins and folic acid.',
        vitals: {
          bloodPressure: '115/70',
          heartRate: '78',
          temperature: '36.6',
          respiratoryRate: '16',
          weight: '62',
          height: '165',
          bmi: '22.8'
        }
      },
      {
        date: '2022-09-15',
        reason: 'Annual check-up',
        diagnosis: 'Healthy',
        treatment: 'No treatment required',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'Patient in good health. Discussed family planning options.',
        vitals: {
          bloodPressure: '110/70',
          heartRate: '72',
          temperature: '36.5',
          respiratoryRate: '14',
          weight: '60',
          height: '165',
          bmi: '22.0'
        }
      },
    ],
  },
  {
    id: 'patient-6',
    name: 'Thabo Mbeki',
    idNumber: '6905125012087',
    dateOfBirth: '1969-05-12',
    gender: 'Male',
    contactNumber: '**********',
    email: '<EMAIL>',
    address: '78 Freedom Road, Eastside',
    emergencyContact: 'Zanele Mbeki (Wife) - **********',
    medicalAidNumber: 'MA789012',
    medicalAidScheme: 'Discovery Health',
    bloodType: 'B+',
    allergies: [],
    chronicConditions: ['Hypertension', 'High Cholesterol'],
    currentMedications: ['Lisinopril 20mg daily', 'Atorvastatin 10mg daily'],
    pastVisits: [
      {
        date: '2023-05-15',
        reason: 'Hypertension follow-up',
        diagnosis: 'Uncontrolled hypertension',
        treatment: 'Increased Lisinopril dosage to 20mg daily',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'BP still elevated despite medication compliance. Increased dosage and scheduled 2-week follow-up.',
        vitals: {
          bloodPressure: '145/95',
          heartRate: '78',
          temperature: '36.8',
          respiratoryRate: '16',
          weight: '88',
          height: '175',
          bmi: '28.7'
        }
      },
      {
        date: '2023-04-01',
        reason: 'Hypertension follow-up',
        diagnosis: 'Hypertension, poorly controlled',
        treatment: 'Continue current medication, dietary advice',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'Patient reports stress at work. Advised on stress management techniques and salt reduction.',
        vitals: {
          bloodPressure: '140/90',
          heartRate: '76',
          temperature: '36.7',
          respiratoryRate: '16',
          weight: '90',
          height: '175',
          bmi: '29.4'
        }
      },
    ],
  },
  {
    id: 'patient-7',
    name: 'Nomsa Khumalo',
    idNumber: '9401025012087',
    dateOfBirth: '1994-01-02',
    gender: 'Female',
    contactNumber: '**********',
    email: '<EMAIL>',
    address: '15 Sunshine Road, Northside',
    emergencyContact: 'Thembi Khumalo (Mother) - **********',
    medicalAidNumber: 'MA345678',
    medicalAidScheme: 'Bonitas',
    bloodType: 'AB-',
    allergies: ['Aspirin'],
    chronicConditions: ['Migraine'],
    currentMedications: ['Sumatriptan as needed'],
    pastVisits: [
      {
        date: '2023-04-10',
        reason: 'Migraine follow-up',
        diagnosis: 'Chronic migraine',
        treatment: 'Prescribed Sumatriptan for acute attacks',
        doctorId: '5',
        doctorName: 'Dr. Sarah Johnson',
        notes: 'Patient reports 2-3 migraines per week. Identified triggers include stress and lack of sleep.',
        vitals: {
          bloodPressure: '115/75',
          heartRate: '68',
          temperature: '36.5',
          respiratoryRate: '14',
          weight: '58',
          height: '162',
          bmi: '22.1'
        }
      },
    ],
  },
  {
    id: 'patient-3',
    name: 'David Nkosi',
    idNumber: '1505085012087',
    dateOfBirth: '2015-05-08',
    gender: 'Male',
    contactNumber: '**********',
    email: '<EMAIL>',
    address: '89 Maple Street, Southside',
    emergencyContact: 'Sarah Nkosi (Mother) - **********',
    medicalAidNumber: 'MA456789',
    medicalAidScheme: 'Momentum Health',
    bloodType: 'O-',
    allergies: [],
    chronicConditions: [],
    currentMedications: [],
    pastVisits: [
      {
        date: '2023-05-10',
        reason: 'Vaccination',
        diagnosis: 'Healthy child, routine vaccination',
        treatment: 'MMR vaccine administered',
        doctorId: '6',
        doctorName: 'Dr. Michael Ndlovu',
        notes: 'Child is developing normally. No adverse reactions to previous vaccines. Provided vaccination certificate.',
        vitals: {
          bloodPressure: 'N/A',
          heartRate: '95',
          temperature: '36.4',
          respiratoryRate: '22',
          weight: '22',
          height: '115',
          bmi: '16.6'
        }
      },
      {
        date: '2023-01-15',
        reason: 'Annual check-up',
        diagnosis: 'Healthy child',
        treatment: 'No treatment required',
        doctorId: '6',
        doctorName: 'Dr. Michael Ndlovu',
        notes: 'Growth and development on track. Height and weight within normal percentiles.',
        vitals: {
          bloodPressure: 'N/A',
          heartRate: '98',
          temperature: '36.6',
          respiratoryRate: '24',
          weight: '20',
          height: '110',
          bmi: '16.5'
        }
      },
    ],
  },
  {
    id: 'patient-13',
    name: 'Amahle Dlamini',
    idNumber: '1908125012087',
    dateOfBirth: '2019-08-12',
    gender: 'Female',
    contactNumber: '**********',
    email: '<EMAIL>',
    address: '45 Rose Avenue, Westside',
    emergencyContact: 'Nomsa Dlamini (Mother) - **********',
    medicalAidNumber: 'MA567890',
    medicalAidScheme: 'Discovery Health',
    bloodType: 'A+',
    allergies: [],
    chronicConditions: [],
    currentMedications: [],
    pastVisits: [
      {
        date: '2023-05-10',
        reason: 'Routine check-up',
        diagnosis: 'Healthy child',
        treatment: 'No treatment required',
        doctorId: '6',
        doctorName: 'Dr. Michael Ndlovu',
        notes: 'Growth and development appropriate for age. Weight: 18kg, Height: 102cm. Parent reports no concerns.',
        vitals: {
          bloodPressure: 'N/A',
          heartRate: '105',
          temperature: '36.5',
          respiratoryRate: '26',
          weight: '18',
          height: '102',
          bmi: '17.3'
        }
      },
      {
        date: '2022-12-20',
        reason: 'Cold symptoms',
        diagnosis: 'Upper respiratory tract infection',
        treatment: 'Symptomatic treatment, rest',
        doctorId: '6',
        doctorName: 'Dr. Michael Ndlovu',
        notes: 'Mild cold symptoms. No fever. Advised increased fluids and rest.',
        vitals: {
          bloodPressure: 'N/A',
          heartRate: '110',
          temperature: '36.8',
          respiratoryRate: '28',
          weight: '17',
          height: '98',
          bmi: '17.7'
        }
      },
    ],
  },
  {
    id: 'patient-14',
    name: 'Themba Moyo',
    idNumber: '1703155012087',
    dateOfBirth: '2017-03-15',
    gender: 'Male',
    contactNumber: '**********',
    email: '<EMAIL>',
    address: '67 Pine Street, Eastside',
    emergencyContact: 'Thandi Moyo (Mother) - **********',
    medicalAidNumber: 'MA678901',
    medicalAidScheme: 'Bonitas',
    bloodType: 'B+',
    allergies: [],
    chronicConditions: [],
    currentMedications: [],
    pastVisits: [
      {
        date: '2023-04-20',
        reason: 'Persistent cough',
        diagnosis: 'Bronchitis',
        treatment: 'Prescribed cough syrup and advised rest',
        doctorId: '6',
        doctorName: 'Dr. Michael Ndlovu',
        notes: 'Cough persisting for 2 weeks, worse at night. No fever. Chest clear on examination.',
        vitals: {
          bloodPressure: 'N/A',
          heartRate: '100',
          temperature: '36.7',
          respiratoryRate: '24',
          weight: '20',
          height: '108',
          bmi: '17.1'
        }
      },
      {
        date: '2023-01-10',
        reason: 'Annual check-up',
        diagnosis: 'Healthy child',
        treatment: 'No treatment required',
        doctorId: '6',
        doctorName: 'Dr. Michael Ndlovu',
        notes: 'Child developing normally. All milestones met. Parent reports good appetite and sleep.',
        vitals: {
          bloodPressure: 'N/A',
          heartRate: '95',
          temperature: '36.4',
          respiratoryRate: '22',
          weight: '19',
          height: '105',
          bmi: '17.2'
        }
      },
    ],
  },
];

// Mock prescriptions
export const MOCK_PRESCRIPTIONS = [
  {
    id: 'prescription-1',
    patientId: 'patient-1',
    patientName: 'John Smith',
    doctorId: '5',
    doctorName: 'Dr. Sarah Johnson',
    date: '2023-05-15',
    diagnosis: 'Hypertension',
    medications: [
      {
        name: 'Lisinopril',
        dosage: '10mg',
        frequency: 'Once daily',
        duration: '3 months',
        quantity: '90 tablets',
        instructions: 'Take in the morning with food',
        refills: 2,
      },
    ],
    notes: "Patient's blood pressure is well-controlled with current dosage. Continue monitoring.",
    status: 'active',
    dispensed: false,
    collectionReminded: false,
    expiryDate: '2023-08-15',
    pharmacy: 'Central City Pharmacy',
  },
  {
    id: 'prescription-2',
    patientId: 'patient-2',
    patientName: 'Mary Johnson',
    doctorId: '5',
    doctorName: 'Dr. Sarah Johnson',
    date: '2023-05-20',
    diagnosis: 'Pregnancy',
    medications: [
      {
        name: 'Prenatal Vitamins',
        dosage: '1 tablet',
        frequency: 'Once daily',
        duration: '1 month',
        quantity: '30 tablets',
        instructions: 'Take with food',
        refills: 5,
      },
      {
        name: 'Folic Acid',
        dosage: '5mg',
        frequency: 'Once daily',
        duration: '1 month',
        quantity: '30 tablets',
        instructions: 'Take in the morning',
        refills: 5,
      },
    ],
    notes: 'Patient is in second trimester of pregnancy. Continue prenatal care.',
    status: 'active',
    dispensed: true,
    collectionReminded: true,
    expiryDate: '2023-06-20',
    pharmacy: 'Westside Pharmacy',
    dispensedDate: '2023-05-22',
  },
  {
    id: 'prescription-3',
    patientId: 'patient-6',
    patientName: 'Thabo Mbeki',
    doctorId: '5',
    doctorName: 'Dr. Sarah Johnson',
    date: '2023-05-15',
    diagnosis: 'Hypertension, Hypercholesterolemia',
    medications: [
      {
        name: 'Lisinopril',
        dosage: '20mg',
        frequency: 'Once daily',
        duration: '1 month',
        quantity: '30 tablets',
        instructions: 'Take in the morning with food',
        refills: 2,
      },
      {
        name: 'Atorvastatin',
        dosage: '10mg',
        frequency: 'Once daily',
        duration: '1 month',
        quantity: '30 tablets',
        instructions: 'Take in the evening',
        refills: 2,
      },
    ],
    notes: 'Increased Lisinopril dosage due to uncontrolled hypertension. Follow up in 2 weeks.',
    status: 'active',
    dispensed: false,
    collectionReminded: false,
    expiryDate: '2023-06-15',
    pharmacy: 'Central City Pharmacy',
  },
  {
    id: 'prescription-4',
    patientId: 'patient-7',
    patientName: 'Nomsa Khumalo',
    doctorId: '5',
    doctorName: 'Dr. Sarah Johnson',
    date: '2023-04-10',
    diagnosis: 'Chronic Migraine',
    medications: [
      {
        name: 'Sumatriptan',
        dosage: '50mg',
        frequency: 'As needed for migraine attacks, max 2 tablets per day',
        duration: '1 month',
        quantity: '9 tablets',
        instructions: 'Take at first sign of migraine. Do not take more than 2 tablets in 24 hours.',
        refills: 3,
      },
      {
        name: 'Propranolol',
        dosage: '40mg',
        frequency: 'Twice daily',
        duration: '1 month',
        quantity: '60 tablets',
        instructions: 'Take with food in the morning and evening',
        refills: 3,
      },
    ],
    notes: 'Patient experiences 2-3 migraines per week. Starting preventive treatment with Propranolol.',
    status: 'active',
    dispensed: true,
    collectionReminded: true,
    expiryDate: '2023-05-10',
    pharmacy: 'Northside Pharmacy',
    dispensedDate: '2023-04-11',
  },
  {
    id: 'prescription-5',
    patientId: 'patient-15',
    patientName: 'Zanele Sithole',
    doctorId: '7',
    doctorName: 'Dr. Thabo Molefe',
    date: '2023-05-15',
    diagnosis: 'Tooth extraction (lower right molar)',
    medications: [
      {
        name: 'Amoxicillin',
        dosage: '500mg',
        frequency: 'Three times daily',
        duration: '7 days',
        quantity: '21 capsules',
        instructions: 'Take with food every 8 hours',
        refills: 0,
      },
      {
        name: 'Ibuprofen',
        dosage: '400mg',
        frequency: 'Every 6 hours as needed for pain',
        duration: '5 days',
        quantity: '20 tablets',
        instructions: 'Take with food. Do not exceed 4 tablets in 24 hours.',
        refills: 0,
      },
    ],
    notes: 'Post-extraction antibiotics and pain management. Follow up if pain persists beyond 5 days.',
    status: 'active',
    dispensed: true,
    collectionReminded: true,
    expiryDate: '2023-05-22',
    pharmacy: 'Southside Pharmacy',
    dispensedDate: '2023-05-15',
  },
  {
    id: 'prescription-6',
    patientId: 'patient-14',
    patientName: 'Themba Moyo',
    doctorId: '6',
    doctorName: 'Dr. Michael Ndlovu',
    date: '2023-04-20',
    diagnosis: 'Bronchitis',
    medications: [
      {
        name: 'Pediatric Cough Syrup',
        dosage: '5ml',
        frequency: 'Three times daily',
        duration: '7 days',
        quantity: '100ml bottle',
        instructions: 'Give with food. Shake well before use.',
        refills: 0,
      },
    ],
    notes: 'Persistent cough for 2 weeks. Prescribed pediatric cough syrup for symptom relief.',
    status: 'completed',
    dispensed: true,
    collectionReminded: true,
    expiryDate: '2023-04-27',
    pharmacy: 'Eastside Pharmacy',
    dispensedDate: '2023-04-20',
  },
];

// Initialize mock data in AsyncStorage
export const initializeMockDoctorData = async () => {
  try {
    // Always clear and reinitialize data to ensure we have the latest mock data
    await AsyncStorage.removeItem('@doctor_appointments');
    await AsyncStorage.removeItem('@doctor_patients');
    await AsyncStorage.removeItem('@doctor_prescriptions');

    // Initialize with mock data
    await AsyncStorage.setItem('@doctor_appointments', JSON.stringify(MOCK_DOCTOR_APPOINTMENTS));
    await AsyncStorage.setItem('@doctor_patients', JSON.stringify(MOCK_PATIENT_RECORDS));
    await AsyncStorage.setItem('@doctor_prescriptions', JSON.stringify(MOCK_PRESCRIPTIONS));

    console.log('Mock doctor data initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing mock doctor data:', error);
    return false;
  }
};

// Get doctor's appointments for a specific date
export const getDoctorAppointments = async (doctorId, date) => {
  try {
    console.log(`Getting appointments for doctor ID: ${doctorId}, date: ${date}`);

    const appointmentsJson = await AsyncStorage.getItem('@doctor_appointments');
    const appointments = appointmentsJson ? JSON.parse(appointmentsJson) : [];

    console.log(`Total appointments in storage: ${appointments.length}`);

    // Filter appointments by doctor and date
    const filteredAppointments = appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date).toDateString();
      const targetDate = date ? new Date(date).toDateString() : new Date().toDateString();
      const matchesDoctor = appointment.doctorId === doctorId;
      const matchesDate = appointmentDate === targetDate;

      if (matchesDoctor && matchesDate) {
        console.log(`Found matching appointment: ${appointment.id}, ${appointment.patientName}, ${appointment.time}`);
      }

      return matchesDoctor && matchesDate;
    });

    console.log(`Filtered appointments count: ${filteredAppointments.length}`);

    // Sort by time
    const sortedAppointments = filteredAppointments.sort((a, b) => {
      return new Date(`2000/01/01 ${a.time}`) - new Date(`2000/01/01 ${b.time}`);
    });

    return sortedAppointments;
  } catch (error) {
    console.error('Error getting doctor appointments:', error);
    return [];
  }
};

// Get patient details
export const getPatientDetails = async (patientId) => {
  try {
    // First try to get from AsyncStorage
    const patientsJson = await AsyncStorage.getItem('@doctor_patients');
    const storedPatients = patientsJson ? JSON.parse(patientsJson) : [];

    // Look for patient in stored data
    let patient = storedPatients.find(patient => patient.id === patientId);

    // If not found in storage, fall back to mock data
    if (!patient) {
      patient = MOCK_PATIENT_RECORDS.find(patient => patient.id === patientId);
    }

    return patient || null;
  } catch (error) {
    console.error('Error getting patient details:', error);
    // If there's an error, try to return from mock data as fallback
    return MOCK_PATIENT_RECORDS.find(patient => patient.id === patientId) || null;
  }
};

// Create a new prescription
export const createPrescription = async (prescriptionData) => {
  try {
    const prescriptionsJson = await AsyncStorage.getItem('@doctor_prescriptions');
    const prescriptions = prescriptionsJson ? JSON.parse(prescriptionsJson) : [];

    const newPrescription = {
      id: `prescription-${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      status: 'active',
      dispensed: false,
      collectionReminded: false,
      ...prescriptionData,
    };

    const updatedPrescriptions = [...prescriptions, newPrescription];
    await AsyncStorage.setItem('@doctor_prescriptions', JSON.stringify(updatedPrescriptions));

    return newPrescription;
  } catch (error) {
    console.error('Error creating prescription:', error);
    return null;
  }
};

// Send prescription collection reminder
export const sendPrescriptionReminder = async (prescriptionId) => {
  try {
    const prescriptionsJson = await AsyncStorage.getItem('@doctor_prescriptions');
    const prescriptions = prescriptionsJson ? JSON.parse(prescriptionsJson) : [];

    const updatedPrescriptions = prescriptions.map(prescription =>
      prescription.id === prescriptionId
        ? { ...prescription, collectionReminded: true }
        : prescription
    );

    await AsyncStorage.setItem('@doctor_prescriptions', JSON.stringify(updatedPrescriptions));

    // In a real app, this would trigger an SMS or push notification
    console.log(`Reminder sent for prescription ${prescriptionId}`);

    return true;
  } catch (error) {
    console.error('Error sending prescription reminder:', error);
    return false;
  }
};

export default {
  MOCK_DOCTOR_USERS,
  MOCK_DOCTOR_APPOINTMENTS,
  MOCK_PATIENT_RECORDS,
  MOCK_PRESCRIPTIONS,
  initializeMockDoctorData,
  getDoctorAppointments,
  getPatientDetails,
  createPrescription,
  sendPrescriptionReminder,
};
