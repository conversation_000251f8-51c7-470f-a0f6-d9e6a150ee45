import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
  Modal,
  Animated,
  Dimensions,
  Vibration,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';
import CustomInput from '../components/CustomInput';
import GradientBackground from '../components/GradientBackground';
import { useMockAuth } from '../context/MockAuthContext';
import {
  HEALTHCARE_PROVIDERS,
  addAppointment,
  initializeMockHealthData
} from '../data/mockHealthData';

const { height } = Dimensions.get('window');

const BookingScreen = ({ navigation }) => {
  const { currentUser } = useMockAuth();
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('');
  const [reason, setReason] = useState('');
  const [contactNumber, setContactNumber] = useState(currentUser?.phone || '');
  const [showProviderModal, setShowProviderModal] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [providers, setProviders] = useState([]);

  // Fallback providers in case import fails
  const fallbackProviders = [
    {
      id: 'provider-1',
      name: 'Dr. Sarah Johnson',
      specialty: 'General Practitioner',
      location: 'Central City Clinic',
    },
    {
      id: 'provider-2',
      name: 'Dr. Michael Ndlovu',
      specialty: 'Pediatrician',
      location: 'Sunshine Children\'s Clinic',
    },
    {
      id: 'provider-3',
      name: 'Dr. Thabo Molefe',
      specialty: 'Dentist',
      location: 'Bright Smile Dental Clinic',
    },
    {
      id: 'provider-4',
      name: 'Nurse Nomsa Dlamini',
      specialty: 'Vaccination Nurse',
      location: 'Community Health Center',
    },
    {
      id: 'provider-5',
      name: 'Dr. Elizabeth Nkosi',
      specialty: 'Obstetrician',
      location: 'Maternal Care Center',
    },
  ];

  // Initialize data on component mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        await initializeMockHealthData();
        // Use imported providers or fallback
        const availableProviders = HEALTHCARE_PROVIDERS && HEALTHCARE_PROVIDERS.length > 0
          ? HEALTHCARE_PROVIDERS
          : fallbackProviders;
        setProviders(availableProviders);
        console.log('Providers loaded:', availableProviders.length);
      } catch (error) {
        console.error('Error loading providers:', error);
        // Use fallback providers
        setProviders(fallbackProviders);
      }
    };

    initializeData();
  }, []);



  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDateObj, setSelectedDateObj] = useState(null);

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);
    // First day of the week for the first day of month
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const today = new Date();

    // Generate 42 days (6 weeks) to fill the calendar grid
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const isCurrentMonth = date.getMonth() === month;
      const isToday = date.toDateString() === today.toDateString();
      const isPast = date < today && !isToday;

      days.push({
        date: date,
        day: date.getDate(),
        isCurrentMonth,
        isToday,
        isPast,
        fullDate: date.toLocaleDateString(),
        dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
        monthName: date.toLocaleDateString('en-US', { month: 'short' }),
        year: date.getFullYear()
      });
    }

    return days;
  };

  const calendarDays = generateCalendarDays();

  // Available time slots
  const availableTimes = [
    '08:00 AM', '09:00 AM', '10:00 AM', '11:00 AM',
    '12:00 PM', '01:00 PM', '02:00 PM', '03:00 PM', '04:00 PM'
  ];



  // Modal functions
  const openDateModal = () => {
    console.log('Opening date modal...'); // Debug log
    setShowDateModal(true);
  };

  const closeDateModal = () => {
    console.log('Closing date modal...'); // Debug log
    setShowDateModal(false);
  };

  const handleDateSelection = (dateObj) => {
    // Don't allow selection of past dates
    if (dateObj.isPast) {
      return;
    }

    // Haptic feedback
    if (Platform.OS === 'ios') {
      Vibration.vibrate(10);
    }

    setSelectedDate(dateObj);
    setSelectedDateObj(dateObj);
    closeDateModal();
  };

  // Navigate months
  const goToPreviousMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() - 1);
    setCurrentMonth(newMonth);
  };

  const goToNextMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() + 1);
    setCurrentMonth(newMonth);
  };

  // Handle booking submission
  const handleSubmit = async () => {
    // Validate form
    if (!selectedDate || !selectedTime || !selectedProvider || !reason || !contactNumber) {
      Alert.alert('Missing Information', 'Please fill in all fields to book your appointment.');
      return;
    }

    // Format phone number validation
    const phoneRegex = /^\d{10}$/;
    if (!phoneRegex.test(contactNumber.replace(/\D/g, ''))) {
      Alert.alert('Invalid Phone Number', 'Please enter a valid 10-digit phone number.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Get provider details
      const provider = HEALTHCARE_PROVIDERS.find(p => p.id === selectedProvider);

      // Create appointment data
      const appointmentData = {
        date: selectedDate.fullDate || selectedDate,
        time: selectedTime,
        provider: provider.name,
        providerSpecialty: provider.specialty,
        location: provider.location,
        reason,
        contactNumber,
        userId: currentUser?.id || 'guest',
        userName: currentUser?.name || 'Guest User',
      };

      // Add appointment to storage
      const result = await addAppointment(appointmentData);

      if (result) {
        Alert.alert(
          'Appointment Booked',
          `Your appointment with ${provider.name} has been scheduled for ${selectedDate.fullDate || selectedDate} at ${selectedTime}.`,
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('NotificationsScreen')
            }
          ]
        );
      } else {
        Alert.alert('Error', 'There was a problem booking your appointment. Please try again.');
      }
    } catch (error) {
      console.error('Error booking appointment:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Simple provider selection modal
  const renderProviderModal = () => {
    console.log('Rendering provider modal, visible:', showProviderModal);
    console.log('Local providers:', providers);
    console.log('Local providers length:', providers?.length);

    if (!showProviderModal) return null;

    return (
      <Modal
        visible={showProviderModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowProviderModal(false)}
      >
        <View style={styles.simpleModalOverlay}>
          <View style={styles.simpleModalContainer}>
            <View style={styles.simpleModalHeader}>
              <Text style={styles.simpleModalTitle}>Select Healthcare Provider</Text>
              <TouchableOpacity
                onPress={() => setShowProviderModal(false)}
                style={styles.simpleCloseButton}
              >
                <Icon name="close" size={24} color={COLORS.black} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.simpleModalContent}>
              {providers.map((provider) => (
                <TouchableOpacity
                  key={provider.id}
                  style={[
                    styles.simpleProviderItem,
                    selectedProvider === provider.id && styles.simpleSelectedItem
                  ]}
                  onPress={() => {
                    console.log('Selected provider:', provider.name);
                    setSelectedProvider(provider.id);
                    setShowProviderModal(false);
                  }}
                >
                  <View>
                    <Text style={styles.simpleProviderName}>{provider.name}</Text>
                    <Text style={styles.simpleProviderSpecialty}>{provider.specialty}</Text>
                    <Text style={styles.simpleProviderLocation}>{provider.location}</Text>
                  </View>
                  {selectedProvider === provider.id && (
                    <Icon name="check-circle" size={24} color={COLORS.primaryGreen} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  // Unlimited date selection modal with calendar
  const renderDateModal = () => {
    console.log('Rendering date modal, visible:', showDateModal);

    if (!showDateModal) return null;

    // Generate calendar days for current month view
    const generateCalendarDays = () => {
      const year = currentMonth.getFullYear();
      const month = currentMonth.getMonth();

      // First day of the month
      const firstDay = new Date(year, month, 1);
      // Last day of the month
      const lastDay = new Date(year, month + 1, 0);
      // First day of the week for the first day of month (Sunday = 0)
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());

      const days = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time for accurate comparison

      // Generate 42 days (6 weeks) to fill the calendar grid
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        date.setHours(0, 0, 0, 0); // Reset time for accurate comparison

        const isCurrentMonth = date.getMonth() === month;
        const isToday = date.getTime() === today.getTime();
        const isPast = date < today;

        days.push({
          date: date,
          day: date.getDate(),
          isCurrentMonth,
          isToday,
          isPast,
          fullDate: date.toLocaleDateString(),
          display: date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })
        });
      }

      return days;
    };

    const calendarDays = generateCalendarDays();

    return (
      <Modal
        visible={showDateModal}
        transparent={true}
        animationType="slide"
        onRequestClose={closeDateModal}
      >
        <View style={styles.simpleModalOverlay}>
          <View style={[styles.simpleModalContainer, { height: '80%' }]}>
            <View style={styles.simpleModalHeader}>
              <Text style={styles.simpleModalTitle}>Select Any Date</Text>
              <TouchableOpacity
                onPress={closeDateModal}
                style={styles.simpleCloseButton}
              >
                <Icon name="close" size={24} color={COLORS.black} />
              </TouchableOpacity>
            </View>

            {/* Month Navigation */}
            <View style={styles.monthNavigation}>
              <TouchableOpacity
                style={styles.monthNavButton}
                onPress={() => {
                  const newMonth = new Date(currentMonth);
                  newMonth.setMonth(currentMonth.getMonth() - 1);
                  setCurrentMonth(newMonth);
                }}
              >
                <Icon name="chevron-left" size={24} color={COLORS.primaryGreen} />
              </TouchableOpacity>

              <Text style={styles.monthYearText}>
                {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
              </Text>

              <TouchableOpacity
                style={styles.monthNavButton}
                onPress={() => {
                  const newMonth = new Date(currentMonth);
                  newMonth.setMonth(currentMonth.getMonth() + 1);
                  setCurrentMonth(newMonth);
                }}
              >
                <Icon name="chevron-right" size={24} color={COLORS.primaryGreen} />
              </TouchableOpacity>
            </View>

            {/* Days of week header */}
            <View style={styles.daysOfWeekHeader}>
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <Text key={day} style={styles.dayOfWeekText}>{day}</Text>
              ))}
            </View>

            {/* Calendar Grid */}
            <ScrollView style={styles.calendarScrollView}>
              <View style={styles.calendarGrid}>
                {calendarDays.map((dateObj, index) => {
                  const isSelected = selectedDate?.fullDate === dateObj.fullDate;

                  return (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.calendarDay,
                        !dateObj.isCurrentMonth && styles.otherMonthDay,
                        dateObj.isToday && styles.todayDay,
                        isSelected && styles.selectedDay,
                        dateObj.isPast && styles.pastDay
                      ]}
                      onPress={() => {
                        console.log('Selected date:', dateObj.display);
                        setSelectedDate(dateObj);
                        closeDateModal();
                      }}
                      activeOpacity={0.7}
                    >
                      <Text style={[
                        styles.calendarDayText,
                        !dateObj.isCurrentMonth && styles.otherMonthText,
                        dateObj.isToday && !isSelected && styles.todayText,
                        isSelected && styles.selectedDayText,
                        dateObj.isPast && styles.pastDayText
                      ]}>
                        {dateObj.day}
                      </Text>

                      {/* Today indicator */}
                      {dateObj.isToday && !isSelected && (
                        <View style={styles.todayIndicator} />
                      )}

                      {/* Selected indicator */}
                      {isSelected && (
                        <View style={styles.selectedIndicator}>
                          <Icon name="check" size={12} color={COLORS.white} />
                        </View>
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  // Time picker state
  const [selectedHour, setSelectedHour] = useState(9);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState('AM');

  // Generate hours and minutes
  const hours = Array.from({ length: 12 }, (_, i) => i + 1);
  const minutes = [0, 15, 30, 45];

  // Format time for display
  const formatSelectedTime = () => {
    const hour = selectedHour.toString().padStart(2, '0');
    const minute = selectedMinute.toString().padStart(2, '0');
    return `${hour}:${minute} ${selectedPeriod}`;
  };

  // Popup time picker modal
  const renderTimeModal = () => (
    <Modal
      visible={showTimeModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowTimeModal(false)}
    >
      <View style={styles.popupOverlay}>
        <View style={styles.popupContainer}>
          {/* Header */}
          <View style={styles.popupHeader}>
            <Text style={styles.popupTitle}>Select Time</Text>
            <TouchableOpacity
              onPress={() => setShowTimeModal(false)}
              style={styles.popupCloseButton}
            >
              <Icon name="close" size={20} color={COLORS.gray} />
            </TouchableOpacity>
          </View>

          {/* Time Display */}
          <View style={styles.timeDisplay}>
            <Text style={styles.timeDisplayText}>{formatSelectedTime()}</Text>
          </View>

          {/* Time Controls */}
          <View style={styles.timeControls}>
            {/* Hour Selector */}
            <View style={styles.timeSection}>
              <Text style={styles.timeSectionLabel}>Hour</Text>
              <View style={styles.timeButtons}>
                <TouchableOpacity
                  style={styles.timeButton}
                  onPress={() => setSelectedHour(selectedHour > 1 ? selectedHour - 1 : 12)}
                >
                  <Icon name="minus" size={16} color={COLORS.primaryGreen} />
                </TouchableOpacity>
                <Text style={styles.timeValue}>{selectedHour}</Text>
                <TouchableOpacity
                  style={styles.timeButton}
                  onPress={() => setSelectedHour(selectedHour < 12 ? selectedHour + 1 : 1)}
                >
                  <Icon name="plus" size={16} color={COLORS.primaryGreen} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Minute Selector */}
            <View style={styles.timeSection}>
              <Text style={styles.timeSectionLabel}>Minute</Text>
              <View style={styles.minuteOptions}>
                {minutes.map((minute) => (
                  <TouchableOpacity
                    key={minute}
                    style={[
                      styles.minuteButton,
                      selectedMinute === minute && styles.selectedMinuteButton
                    ]}
                    onPress={() => setSelectedMinute(minute)}
                  >
                    <Text style={[
                      styles.minuteButtonText,
                      selectedMinute === minute && styles.selectedMinuteButtonText
                    ]}>
                      {minute.toString().padStart(2, '0')}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* AM/PM Selector */}
            <View style={styles.timeSection}>
              <Text style={styles.timeSectionLabel}>Period</Text>
              <View style={styles.periodButtons}>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    selectedPeriod === 'AM' && styles.selectedPeriodButton
                  ]}
                  onPress={() => setSelectedPeriod('AM')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    selectedPeriod === 'AM' && styles.selectedPeriodButtonText
                  ]}>
                    AM
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    selectedPeriod === 'PM' && styles.selectedPeriodButton
                  ]}
                  onPress={() => setSelectedPeriod('PM')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    selectedPeriod === 'PM' && styles.selectedPeriodButtonText
                  ]}>
                    PM
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.popupActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowTimeModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={() => {
                setSelectedTime(formatSelectedTime());
                setShowTimeModal(false);
              }}
            >
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Book Appointment</Text>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.formContainer}>
          {/* Provider Selection */}
          <Text style={styles.inputLabel}>Healthcare Provider</Text>
          <TouchableOpacity
            style={styles.selectionButton}
            onPress={() => {
              console.log('Provider button pressed');
              console.log('Local providers available:', !!providers);
              console.log('Local providers count:', providers?.length || 0);
              setShowProviderModal(true);
            }}
          >
            <Text style={[
              styles.selectionText,
              !selectedProvider && styles.placeholderText
            ]}>
              {selectedProvider
                ? providers.find(p => p.id === selectedProvider)?.name
                : 'Select a healthcare provider'}
            </Text>
            <Icon name="chevron-down" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          {/* Date Selection */}
          <Text style={styles.inputLabel}>Appointment Date</Text>
          <TouchableOpacity
            style={styles.selectionButton}
            onPress={openDateModal}
          >
            <Text style={[
              styles.selectionText,
              !selectedDate && styles.placeholderText
            ]}>
              {selectedDate ?
                selectedDate.date.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }) :
                'Select a date'
              }
            </Text>
            <Icon name="calendar" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          {/* Time Selection */}
          <Text style={styles.inputLabel}>Appointment Time</Text>
          <TouchableOpacity
            style={styles.selectionButton}
            onPress={() => setShowTimeModal(true)}
          >
            <Text style={[
              styles.selectionText,
              !selectedTime && styles.placeholderText
            ]}>
              {selectedTime || 'Select a time'}
            </Text>
            <Icon name="clock-outline" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          {/* Reason for Visit */}
          <Text style={styles.inputLabel}>Reason for Visit</Text>
          <CustomInput
            placeholder="Briefly describe your reason for the visit"
            value={reason}
            onChangeText={setReason}
            multiline={true}
            numberOfLines={3}
            style={styles.reasonInput}
          />

          {/* Contact Number */}
          <Text style={styles.inputLabel}>Contact Number</Text>
          <CustomInput
            placeholder="Enter your contact number"
            value={contactNumber}
            onChangeText={setContactNumber}
            keyboardType="phone-pad"
          />

          {/* Submit Button */}
          <CustomButton
            title="Book Appointment"
            onPress={handleSubmit}
            loading={isSubmitting}
            style={styles.submitButton}
          />
        </View>
      </ScrollView>

      {/* Modals */}
      {renderProviderModal()}
      {renderDateModal()}
      {renderTimeModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  scrollView: {
    padding: SPACING.md,
  },
  formContainer: {
    marginBottom: SPACING.xl,
  },
  inputLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
    marginTop: SPACING.md,
  },
  selectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 10,
    padding: SPACING.md,
    backgroundColor: COLORS.white,
  },
  selectionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  placeholderText: {
    color: COLORS.gray,
  },
  reasonInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: SPACING.xl,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 24,
    width: '100%',
    maxHeight: height * 0.8,
    overflow: 'hidden',
    // Enhanced shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  modalHeader: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
    position: 'relative',
  },
  modalTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  modalSubtitle: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontWeight: FONT_WEIGHTS.medium,
  },
  closeButton: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  providerScrollContent: {
    padding: SPACING.md,
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  selectedModalItem: {
    backgroundColor: COLORS.offWhite,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  providerSpecialty: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginBottom: 2,
  },
  providerLocation: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  emptyState: {
    padding: SPACING.xl,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  emptyStateSubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
    textAlign: 'center',
  },
  dateText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  timeText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  // Calendar styles
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    backgroundColor: COLORS.white,
  },
  monthNavButton: {
    padding: SPACING.sm,
    borderRadius: 8,
    backgroundColor: COLORS.offWhite,
  },
  monthYearText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  daysOfWeekHeader: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.offWhite,
  },
  dayOfWeekText: {
    flex: 1,
    textAlign: 'center',
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
  },
  calendarScrollView: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.white,
  },
  calendarDay: {
    width: '14.28%', // 100% / 7 days
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
    borderRadius: 8,
    position: 'relative',
  },
  calendarDayText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  otherMonthDay: {
    opacity: 0.3,
  },
  otherMonthText: {
    color: COLORS.gray,
  },
  todayDay: {
    backgroundColor: COLORS.primaryLightGreen,
    borderRadius: 8,
  },
  todayText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
  },
  selectedDay: {
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 8,
  },
  selectedDayText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
  },
  pastDay: {
    opacity: 0.4,
  },
  pastDayText: {
    color: COLORS.gray,
  },
  todayIndicator: {
    position: 'absolute',
    bottom: 2,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: COLORS.white,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Simple modal styles
  simpleModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  simpleModalContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  simpleModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    backgroundColor: COLORS.white,
  },
  simpleModalTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  simpleCloseButton: {
    padding: SPACING.xs,
  },
  simpleModalContent: {
    maxHeight: 400,
    backgroundColor: COLORS.white,
  },
  simpleProviderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    backgroundColor: COLORS.white,
  },
  simpleSelectedItem: {
    backgroundColor: COLORS.offWhite,
  },
  simpleProviderName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  simpleProviderSpecialty: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginBottom: 2,
  },
  simpleProviderLocation: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  // Calendar styles for unlimited date selection
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    backgroundColor: COLORS.white,
  },
  monthNavButton: {
    padding: SPACING.sm,
    borderRadius: 8,
    backgroundColor: COLORS.offWhite,
  },
  monthYearText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  daysOfWeekHeader: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.offWhite,
  },
  dayOfWeekText: {
    flex: 1,
    textAlign: 'center',
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
  },
  calendarScrollView: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.white,
  },
  calendarDay: {
    width: '14.28%', // 100% / 7 days
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
    borderRadius: 8,
    position: 'relative',
  },
  calendarDayText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  otherMonthDay: {
    opacity: 0.3,
  },
  otherMonthText: {
    color: COLORS.gray,
  },
  todayDay: {
    backgroundColor: COLORS.primaryLightGreen,
    borderRadius: 8,
  },
  todayText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
  },
  selectedDay: {
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 8,
  },
  selectedDayText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
  },
  pastDay: {
    opacity: 0.4,
  },
  pastDayText: {
    color: COLORS.gray,
  },
  todayIndicator: {
    position: 'absolute',
    bottom: 2,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: COLORS.white,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Legacy date card styles (keeping for compatibility)
  dateCardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  selectedDateText: {
    color: COLORS.white,
  },
  // Popup time picker styles
  popupOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  popupContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    width: '100%',
    maxWidth: 350,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  popupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  popupTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  popupCloseButton: {
    padding: SPACING.xs,
  },
  timeDisplay: {
    alignItems: 'center',
    padding: SPACING.lg,
    backgroundColor: COLORS.offWhite,
  },
  timeDisplayText: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  timeControls: {
    padding: SPACING.lg,
  },
  timeSection: {
    marginBottom: SPACING.lg,
  },
  timeSectionLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  timeButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.offWhite,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: SPACING.lg,
  },
  timeValue: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    minWidth: 40,
    textAlign: 'center',
  },
  minuteOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  minuteButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    backgroundColor: COLORS.offWhite,
  },
  selectedMinuteButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  minuteButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
  selectedMinuteButtonText: {
    color: COLORS.white,
  },
  periodButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  periodButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: 8,
    backgroundColor: COLORS.offWhite,
    marginHorizontal: SPACING.sm,
  },
  selectedPeriodButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  periodButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  selectedPeriodButtonText: {
    color: COLORS.white,
  },
  popupActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    marginRight: SPACING.sm,
    borderRadius: 8,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    marginLeft: SPACING.sm,
    borderRadius: 8,
    backgroundColor: COLORS.primaryGreen,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.white,
  },
});

export default BookingScreen;
