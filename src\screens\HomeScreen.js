import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Platform,
  Alert // Added for potential error handling
} from 'react-native';
// Import Moti components
import { MotiView, MotiText } from 'moti';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import { useMockAuth } from '../context/MockAuthContext';

const HomeScreen = ({ navigation }) => {
  const { currentUser, signOut } = useMockAuth();

  const handleLogout = async () => {
    try {
      const result = await signOut();
      if (result.success) {
        console.log('Logged out successfully');
        // Explicitly navigate to Login screen
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        });
      } else {
        console.error('Logout failed:', result.error);
        Alert.alert('Logout Failed', 'Could not log out. Please try again.');
      }
    } catch (error) {
       console.error('An error occurred during logout:', error);
       Alert.alert('Error', 'An unexpected error occurred during logout.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollView}>
        {/* Use MotiView for animated container */}
        <MotiView
          style={styles.header}
          from={{ opacity: 0, translateY: -20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 500 }}
        >
          <Text style={styles.appName}>Let's Talk</Text>
          <Text style={styles.tagline}>Welcome to your dashboard</Text>
        </MotiView>

        <View style={styles.contentContainer}>
          {/* Animate Welcome Container */}
          <MotiView
            style={styles.welcomeContainer}
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500, delay: 100 }} // Stagger with delay
          >
            <Text style={styles.welcomeText}>
              Welcome, {currentUser?.name || 'User'}!
            </Text>
            <Text style={styles.emailText}>
              {currentUser?.email || 'Your email'}
            </Text>
            {currentUser?.idNumber && (
              <Text style={styles.idNumberText}>
                ID: {currentUser.idNumber.replace(/(\d{6})(\d{4})(\d{3})/, '$1 $2 $3')}
              </Text>
            )}
          </MotiView>

          {/* Animate Info Container */}
          <MotiView
            style={styles.infoContainer}
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500, delay: 200 }} // Stagger with delay
          >
            <Text style={styles.infoTitle}>Your Account</Text>
            <Text style={styles.infoText}>
              You are now logged in with mock authentication.
            </Text>
          </MotiView>

          {/* Animate Logout Button */}
          <MotiView
            style={{ width: '100%', alignItems: 'center', marginTop: 'auto' }} // Wrapper to apply animation and maintain layout
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500, delay: 300 }} // Stagger with delay
          >
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Text style={styles.logoutText}>Logout</Text>
            </TouchableOpacity>
          </MotiView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  scrollView: {
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: SPACING.xl, // Added padding to ensure logout button doesn't stick right at the edge
  },
  header: {
    alignItems: 'center',
    marginTop: SPACING.xl,
    marginBottom: SPACING.xl,
    width: '100%',
  },
  appName: {
    fontSize: FONT_SIZES.title,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.xs,
  },
  tagline: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  contentContainer: {
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    flex: 1, // Important for marginTop: 'auto' on logout button wrapper to work
  },
  welcomeContainer: {
    backgroundColor: COLORS.primaryGreen,
    width: '100%',
    padding: SPACING.lg,
    borderRadius: 12,
    marginBottom: SPACING.xl,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  emailText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    opacity: 0.9,
    marginBottom: SPACING.xs,
  },
  idNumberText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    opacity: 0.9,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
    letterSpacing: 1,
  },
  infoContainer: {
    backgroundColor: COLORS.lightGray, // Using theme color now
    width: '100%',
    padding: SPACING.lg,
    borderRadius: 12,
    marginBottom: SPACING.xl,
  },
  infoTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  infoText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    lineHeight: 22,
  },
  logoutButton: {
    // Removed marginTop: 'auto' from here
    backgroundColor: COLORS.lightGray, // Using theme color now
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.xl,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    // marginBottom: SPACING.xl, // Moved margin to wrapper MotiView or adjust scrollView padding
  },
  logoutText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    fontWeight: FONT_WEIGHTS.medium,
  },
});

export default HomeScreen;