import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { getPatientDetails, MOCK_PRESCRIPTIONS, initializeMockDoctorData } from '../../data/mockDoctorData';

const DoctorPatientDetailsScreen = ({ navigation, route }) => {
  const { patientId } = route.params;
  const [loading, setLoading] = useState(true);
  const [patient, setPatient] = useState(null);
  const [prescriptions, setPrescriptions] = useState([]);
  const [activeTab, setActiveTab] = useState('info');

  // Load patient details on component mount
  useEffect(() => {
    loadPatientDetails();
  }, [patientId]);

  // Load patient details
  const loadPatientDetails = async () => {
    setLoading(true);
    try {
      // Ensure mock data is initialized
      await initializeMockDoctorData();

      // Get patient details
      const patientData = await getPatientDetails(patientId);
      console.log('Patient data loaded:', patientData);
      setPatient(patientData);

      // Get patient prescriptions
      const patientPrescriptions = MOCK_PRESCRIPTIONS.filter(
        prescription => prescription.patientId === patientId
      );
      setPrescriptions(patientPrescriptions);
    } catch (error) {
      console.error('Error loading patient details:', error);
      Alert.alert('Error', 'Failed to load patient details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Render patient info tab
  const renderInfoTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Personal Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Full Name:</Text>
          <Text style={styles.infoValue}>{patient.name}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>ID Number:</Text>
          <Text style={styles.infoValue}>{patient.idNumber}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Date of Birth:</Text>
          <Text style={styles.infoValue}>
            {formatDate(patient.dateOfBirth)} ({calculateAge(patient.dateOfBirth)} years)
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Gender:</Text>
          <Text style={styles.infoValue}>{patient.gender}</Text>
        </View>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Contact Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Phone:</Text>
          <Text style={styles.infoValue}>{patient.contactNumber}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Email:</Text>
          <Text style={styles.infoValue}>{patient.email}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Address:</Text>
          <Text style={styles.infoValue}>{patient.address}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Emergency Contact:</Text>
          <Text style={styles.infoValue}>{patient.emergencyContact}</Text>
        </View>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Medical Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Medical Aid:</Text>
          <Text style={styles.infoValue}>
            {patient.medicalAidScheme} - {patient.medicalAidNumber}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Allergies:</Text>
          <View style={styles.tagContainer}>
            {patient.allergies.length > 0 ? (
              patient.allergies.map((allergy, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{allergy}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.infoValue}>No known allergies</Text>
            )}
          </View>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Chronic Conditions:</Text>
          <View style={styles.tagContainer}>
            {patient.chronicConditions.length > 0 ? (
              patient.chronicConditions.map((condition, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{condition}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.infoValue}>None</Text>
            )}
          </View>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Current Medications:</Text>
          <View style={styles.medicationList}>
            {patient.currentMedications.length > 0 ? (
              patient.currentMedications.map((medication, index) => (
                <Text key={index} style={styles.medicationItem}>• {medication}</Text>
              ))
            ) : (
              <Text style={styles.infoValue}>None</Text>
            )}
          </View>
        </View>
      </View>
    </View>
  );

  // Render medical history tab
  const renderHistoryTab = () => (
    <View style={styles.tabContent}>
      {patient.pastVisits.length > 0 ? (
        patient.pastVisits.map((visit, index) => (
          <View key={index} style={styles.visitItem}>
            <View style={styles.visitHeader}>
              <Text style={styles.visitDate}>{formatDate(visit.date)}</Text>
              <Text style={styles.visitDoctor}>{visit.doctorName}</Text>
            </View>
            <View style={styles.visitDetails}>
              <View style={styles.visitRow}>
                <Text style={styles.visitLabel}>Reason:</Text>
                <Text style={styles.visitValue}>{visit.reason}</Text>
              </View>
              <View style={styles.visitRow}>
                <Text style={styles.visitLabel}>Diagnosis:</Text>
                <Text style={styles.visitValue}>{visit.diagnosis}</Text>
              </View>
              <View style={styles.visitRow}>
                <Text style={styles.visitLabel}>Treatment:</Text>
                <Text style={styles.visitValue}>{visit.treatment}</Text>
              </View>
            </View>
          </View>
        ))
      ) : (
        <View style={styles.emptyContainer}>
          <Icon name="clipboard-text-off" size={64} color={COLORS.lightGray} />
          <Text style={styles.emptyText}>No medical history</Text>
          <Text style={styles.emptySubtext}>
            This patient has no recorded past visits.
          </Text>
        </View>
      )}
    </View>
  );

  // Render prescriptions tab
  const renderPrescriptionsTab = () => (
    <View style={styles.tabContent}>
      {prescriptions.length > 0 ? (
        prescriptions.map((prescription, index) => (
          <View key={index} style={styles.prescriptionItem}>
            <View style={styles.prescriptionHeader}>
              <Text style={styles.prescriptionDate}>{prescription.date}</Text>
              <View style={[
                styles.prescriptionStatus,
                prescription.dispensed ? styles.dispensedStatus : styles.pendingStatus
              ]}>
                <Text style={styles.prescriptionStatusText}>
                  {prescription.dispensed ? 'Dispensed' : 'Pending'}
                </Text>
              </View>
            </View>
            <View style={styles.prescriptionDetails}>
              <Text style={styles.prescriptionTitle}>Medications:</Text>
              {prescription.medications.map((medication, medIndex) => (
                <View key={medIndex} style={styles.medicationItem}>
                  <Text style={styles.medicationName}>
                    {medication.name} {medication.dosage}
                  </Text>
                  <Text style={styles.medicationInstructions}>
                    {medication.frequency} for {medication.duration}
                  </Text>
                  <Text style={styles.medicationNotes}>
                    {medication.instructions}
                  </Text>
                </View>
              ))}
            </View>
            <View style={styles.prescriptionFooter}>
              <Text style={styles.prescriptionDoctor}>
                Prescribed by: {prescription.doctorName}
              </Text>
              <Text style={styles.prescriptionExpiry}>
                Expires: {prescription.expiryDate}
              </Text>
            </View>
          </View>
        ))
      ) : (
        <View style={styles.emptyContainer}>
          <Icon name="pill-off" size={64} color={COLORS.lightGray} />
          <Text style={styles.emptyText}>No prescriptions</Text>
          <Text style={styles.emptySubtext}>
            This patient has no active or past prescriptions.
          </Text>
        </View>
      )}

      <TouchableOpacity
        style={styles.newPrescriptionButton}
        onPress={() => navigation.navigate('DoctorPrescription', { patientId })}
      >
        <Icon name="plus" size={20} color={COLORS.white} />
        <Text style={styles.newPrescriptionText}>New Prescription</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Patient Details</Text>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primaryGreen} />
          <Text style={styles.loadingText}>Loading patient details...</Text>
        </View>
      ) : patient ? (
        <>
          {/* Patient Header */}
          <View style={styles.patientHeader}>
            <View style={styles.patientAvatar}>
              <Text style={styles.avatarText}>
                {patient.name.split(' ').map(n => n[0]).join('')}
              </Text>
            </View>
            <View style={styles.patientInfo}>
              <Text style={styles.patientName}>{patient.name}</Text>
              <Text style={styles.patientDetails}>
                {calculateAge(patient.dateOfBirth)} years • {patient.gender}
              </Text>
              <View style={styles.patientActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => Alert.alert('Call', `Calling ${patient.contactNumber}`)}
                >
                  <Icon name="phone" size={16} color={COLORS.primaryGreen} />
                  <Text style={styles.actionText}>Call</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => navigation.navigate('DoctorPrescription', { patientId })}
                >
                  <Icon name="prescription" size={16} color={COLORS.primaryGreen} />
                  <Text style={styles.actionText}>Prescribe</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'info' && styles.activeTab]}
              onPress={() => setActiveTab('info')}
            >
              <Text style={[styles.tabText, activeTab === 'info' && styles.activeTabText]}>
                Information
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'history' && styles.activeTab]}
              onPress={() => setActiveTab('history')}
            >
              <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
                Medical History
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'prescriptions' && styles.activeTab]}
              onPress={() => setActiveTab('prescriptions')}
            >
              <Text style={[styles.tabText, activeTab === 'prescriptions' && styles.activeTabText]}>
                Prescriptions
              </Text>
            </TouchableOpacity>
          </View>

          {/* Tab Content */}
          <ScrollView style={styles.contentContainer}>
            {activeTab === 'info' && renderInfoTab()}
            {activeTab === 'history' && renderHistoryTab()}
            {activeTab === 'prescriptions' && renderPrescriptionsTab()}
          </ScrollView>
        </>
      ) : (
        <View style={styles.errorContainer}>
          <Icon name="account-off" size={64} color={COLORS.error} />
          <Text style={styles.errorText}>Patient Not Found</Text>
          <Text style={styles.errorSubtext}>
            The requested patient could not be found.
          </Text>
          <TouchableOpacity
            style={styles.returnButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.returnButtonText}>Return to Appointments</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  patientHeader: {
    flexDirection: 'row',
    padding: SPACING.md,
    backgroundColor: COLORS.offWhite,
  },
  patientAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.primaryGreen,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  avatarText: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  patientDetails: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  patientActions: {
    flexDirection: 'row',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 15,
    marginRight: SPACING.sm,
  },
  actionText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
    marginLeft: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.sm,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.primaryGreen,
  },
  tabText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  activeTabText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  contentContainer: {
    flex: 1,
  },
  tabContent: {
    padding: SPACING.md,
  },
  infoSection: {
    marginBottom: SPACING.md,
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    // Shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  infoRow: {
    marginBottom: SPACING.sm,
  },
  infoLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 15,
    paddingVertical: 4,
    paddingHorizontal: 8,
    marginRight: 6,
    marginBottom: 6,
  },
  tagText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  medicationList: {
    marginTop: 4,
  },
  medicationItem: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.black,
    marginBottom: 2,
  },
  visitItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  visitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  visitDate: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  visitDoctor: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
  },
  visitDetails: {
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    paddingTop: SPACING.sm,
  },
  visitRow: {
    marginBottom: SPACING.xs,
  },
  visitLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.darkGray,
  },
  visitValue: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  prescriptionItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  prescriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  prescriptionDate: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  prescriptionStatus: {
    paddingVertical: 2,
    paddingHorizontal: SPACING.sm,
    borderRadius: 10,
  },
  pendingStatus: {
    backgroundColor: COLORS.warning + '30', // 30% opacity
  },
  dispensedStatus: {
    backgroundColor: COLORS.success + '30', // 30% opacity
  },
  prescriptionStatusText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  prescriptionDetails: {
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    paddingTop: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  prescriptionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  medicationItem: {
    marginBottom: SPACING.xs,
    paddingLeft: SPACING.sm,
    borderLeftWidth: 2,
    borderLeftColor: COLORS.primaryGreen,
  },
  medicationName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  medicationInstructions: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  medicationNotes: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
    fontStyle: 'italic',
  },
  prescriptionFooter: {
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    paddingTop: SPACING.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  prescriptionDoctor: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  prescriptionExpiry: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  newPrescriptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    borderRadius: 10,
    marginTop: SPACING.sm,
  },
  newPrescriptionText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.white,
    marginLeft: SPACING.xs,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  errorText: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.error,
    marginTop: SPACING.md,
  },
  errorSubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginTop: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  returnButton: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    borderRadius: 10,
  },
  returnButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.white,
  },
});

export default DoctorPatientDetailsScreen;
