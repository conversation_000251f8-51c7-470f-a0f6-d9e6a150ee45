// Load environment variables
import { Platform } from 'react-native';

// Get environment variables
// In a real app, you would use a library like react-native-dotenv or react-native-config
// For this example, we'll hardcode the values from .env.local
const ENV = {
  COGNITO_REGION: 'eu-north-1',
  COGNITO_USER_POOL_ID: 'eu-north-1_0oBkZJyF1',
  COGNITO_USER_POOL_WEB_CLIENT_ID: '3f3asntl66gcbkht1tqjiaomcs',
  COGNITO_DOMAIN: 'https://letstalk-citizen-webapp.auth.eu-north-1.amazoncognito.com',
};

// Make environment variables available
export default {
  // AWS Cognito
  COGNITO_REGION: ENV.COGNITO_REGION,
  COGNITO_USER_POOL_ID: ENV.COGNITO_USER_POOL_ID,
  COGNITO_USER_POOL_WEB_CLIENT_ID: ENV.COGNITO_USER_POOL_WEB_CLIENT_ID,
  COGNITO_DOMAIN: ENV.COGNITO_DOMAIN,
  
  // App specific
  APP_NAME: 'Lets Talk',
  APP_VERSION: '1.0.0',
  
  // Platform
  IS_ANDROID: Platform.OS === 'android',
  IS_IOS: Platform.OS === 'ios',
};
