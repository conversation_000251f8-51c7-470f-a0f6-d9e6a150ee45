import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { useMockAuth } from '../../context/MockAuthContext';
import mockCallData from '../../data/mockCallData';

const AdminCallDetailsScreen = ({ navigation, route }) => {
  const { callId } = route.params;
  const { currentUser } = useMockAuth();
  const [call, setCall] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState({});
  const [referenceNumber, setReferenceNumber] = useState('');
  const [notes, setNotes] = useState('');
  const [category, setCategory] = useState('');
  const [priority, setPriority] = useState('medium');
  const [callEnded, setCallEnded] = useState(false);

  // Load call data on mount
  useEffect(() => {
    loadCallData();
  }, [callId]);

  // Load call data
  const loadCallData = async () => {
    try {
      setLoading(true);
      const queue = await mockCallData.getCallQueue();
      const foundCall = queue.find(c => c.id === callId);

      if (foundCall) {
        setCall(foundCall);

        // Generate reference number
        const refNumber = mockCallData.generateReferenceNumber();
        setReferenceNumber(refNumber);
      } else {
        Alert.alert('Error', 'Call not found');
        navigation.goBack();
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading call data:', error);
      setLoading(false);
      Alert.alert('Error', 'Failed to load call data');
    }
  };

  // Handle next step
  const handleNextStep = () => {
    if (currentStep < mockCallData.STANDARD_QUESTIONS.length) {
      // Validate current step
      const currentQuestion = mockCallData.STANDARD_QUESTIONS[currentStep];

      if (currentQuestion.required && !answers[currentQuestion.id]) {
        Alert.alert('Required Field', 'Please answer this question before proceeding');
        return;
      }

      setCurrentStep(currentStep + 1);
    }
  };

  // Handle previous step
  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle answer change
  const handleAnswerChange = (questionId, value) => {
    setAnswers({
      ...answers,
      [questionId]: value
    });
  };

  // Handle end call
  const handleEndCall = async () => {
    try {
      // Get the selected category
      const selectedCategory = category || 'other';

      // Find the category object to get service providers
      const categoryObj = mockCallData.CALL_CATEGORIES.find(c => c.id === selectedCategory);

      // Auto-assign the first service provider for this category
      const serviceProvider = categoryObj && categoryObj.serviceProviders && categoryObj.serviceProviders.length > 0
        ? categoryObj.serviceProviders[0]
        : 'General Maintenance Technician';

      // Create issue with service provider assignment
      const issueData = {
        callId,
        userId: call.userId,
        userName: call.userName,
        userIdNumber: call.userIdNumber,
        referenceNumber,
        category: selectedCategory,
        description: answers.description || 'No description provided',
        status: 'new',
        handledBy: currentUser.name,
        adminId: currentUser.id,
        priority,
        answers,
        notes,
        assignedServiceProvider: serviceProvider,
        assignedAt: new Date().toISOString(),
        assignedBy: currentUser.name
      };

      await mockCallData.addIssue(issueData);

      // Remove call from queue
      await mockCallData.removeCallFromQueue(callId);

      setCallEnded(true);

      Alert.alert(
        'Call Ended',
        `Call has been ended and issue created with reference number: ${referenceNumber}\n\nService Provider: ${serviceProvider}`,
        [
          {
            text: 'View Issues',
            onPress: () => navigation.navigate('AdminIssues')
          },
          {
            text: 'Back to Queue',
            onPress: () => navigation.navigate('AdminCallQueue')
          },
        ]
      );
    } catch (error) {
      console.error('Error ending call:', error);
      Alert.alert('Error', 'Failed to end call');
    }
  };

  // Render input based on question type
  const renderQuestionInput = (question) => {
    // Check if question should be shown based on conditional logic
    if (question.conditionalOn) {
      const { field, value } = question.conditionalOn;
      if (answers[field] !== value) {
        return null;
      }
    }

    switch (question.type) {
      case 'text':
        return (
          <TextInput
            style={styles.textInput}
            value={answers[question.id] || ''}
            onChangeText={(text) => handleAnswerChange(question.id, text)}
            placeholder="Enter your answer"
          />
        );

      case 'textarea':
        return (
          <TextInput
            style={styles.textareaInput}
            value={answers[question.id] || ''}
            onChangeText={(text) => handleAnswerChange(question.id, text)}
            placeholder="Enter your answer"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        );

      case 'select':
        return (
          <View style={styles.selectContainer}>
            {question.options.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.selectOption,
                  answers[question.id] === option.value && styles.selectedOption
                ]}
                onPress={() => handleAnswerChange(question.id, option.value)}
              >
                <Text style={[
                  styles.selectOptionText,
                  answers[question.id] === option.value && styles.selectedOptionText
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'radio':
        return (
          <View style={styles.radioContainer}>
            {question.options.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={styles.radioOption}
                onPress={() => handleAnswerChange(question.id, option.value)}
              >
                <View style={styles.radioButton}>
                  {answers[question.id] === option.value && (
                    <View style={styles.radioButtonSelected} />
                  )}
                </View>
                <Text style={styles.radioOptionText}>{option.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      default:
        return null;
    }
  };

  // Render current question
  const renderCurrentQuestion = () => {
    if (currentStep < mockCallData.STANDARD_QUESTIONS.length) {
      const question = mockCallData.STANDARD_QUESTIONS[currentStep];

      return (
        <View style={styles.questionContainer}>
          <Text style={styles.questionText}>
            {question.question}
            {question.required && <Text style={styles.requiredIndicator}>*</Text>}
          </Text>
          {renderQuestionInput(question)}

          <View style={styles.navigationButtons}>
            {currentStep > 0 && (
              <TouchableOpacity
                style={[styles.navButton, styles.prevButton]}
                onPress={handlePrevStep}
              >
                <Icon name="arrow-left" size={20} color={COLORS.primaryGreen} />
                <Text style={styles.prevButtonText}>Previous</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.navButton, styles.nextButton]}
              onPress={handleNextStep}
            >
              <Text style={styles.nextButtonText}>Next</Text>
              <Icon name="arrow-right" size={20} color={COLORS.white} />
            </TouchableOpacity>
          </View>
        </View>
      );
    } else {
      // Summary and submission
      return (
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Call Summary</Text>

          <View style={styles.summarySection}>
            <Text style={styles.summarySectionTitle}>Caller Information</Text>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Name:</Text>
              <Text style={styles.summaryValue}>{call?.userName || 'Unknown'}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>ID Number:</Text>
              <Text style={styles.summaryValue}>{call?.userIdNumber || 'Not provided'}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Phone:</Text>
              <Text style={styles.summaryValue}>{call?.phoneNumber || 'Not provided'}</Text>
            </View>
          </View>

          <View style={styles.summarySection}>
            <Text style={styles.summarySectionTitle}>Issue Details</Text>
            {mockCallData.STANDARD_QUESTIONS.map((question) => {
              // Skip conditional questions that shouldn't be shown
              if (question.conditionalOn) {
                const { field, value } = question.conditionalOn;
                if (answers[field] !== value) {
                  return null;
                }
              }

              return (
                <View key={question.id} style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>{question.question}</Text>
                  <Text style={styles.summaryValue}>
                    {answers[question.id] || 'Not provided'}
                  </Text>
                </View>
              );
            })}
          </View>

          <View style={styles.summarySection}>
            <Text style={styles.summarySectionTitle}>Issue Classification</Text>

            <Text style={styles.fieldLabel}>Category</Text>
            <View style={styles.selectContainer}>
              {mockCallData.CALL_CATEGORIES.map((cat) => (
                <TouchableOpacity
                  key={cat.id}
                  style={[
                    styles.selectOption,
                    category === cat.id && styles.selectedOption
                  ]}
                  onPress={() => setCategory(cat.id)}
                >
                  <Text style={[
                    styles.selectOptionText,
                    category === cat.id && styles.selectedOptionText
                  ]}>
                    {cat.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.fieldLabel}>Priority</Text>
            <View style={styles.priorityContainer}>
              {mockCallData.PRIORITY_LEVELS.map((level) => (
                <TouchableOpacity
                  key={level.id}
                  style={[
                    styles.priorityOption,
                    priority === level.id && { backgroundColor: level.color }
                  ]}
                  onPress={() => setPriority(level.id)}
                >
                  <Text style={[
                    styles.priorityText,
                    priority === level.id && { color: COLORS.white }
                  ]}>
                    {level.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.fieldLabel}>Additional Notes</Text>
            <TextInput
              style={styles.notesInput}
              value={notes}
              onChangeText={setNotes}
              placeholder="Enter any additional notes about this call"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />

            <View style={styles.referenceContainer}>
              <Text style={styles.referenceLabel}>Reference Number:</Text>
              <Text style={styles.referenceNumber}>{referenceNumber}</Text>
            </View>

            <TouchableOpacity
              style={styles.endCallButton}
              onPress={() => {
                Alert.alert(
                  'End Call',
                  'Are you sure you want to end this call and create an issue?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'End Call',
                      style: 'destructive',
                      onPress: handleEndCall
                    },
                  ]
                );
              }}
            >
              <Icon name="phone-off" size={20} color={COLORS.white} />
              <Text style={styles.endCallButtonText}>End Call & Create Issue</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading call data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (callEnded) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.navigate('AdminCallQueue')}
          >
            <Icon name="arrow-left" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Call Ended</Text>
          <View style={{ width: 40 }} />
        </View>

        <View style={styles.callEndedContainer}>
          <Icon name="check-circle" size={80} color={COLORS.success} />
          <Text style={styles.callEndedTitle}>Call Successfully Ended</Text>
          <Text style={styles.callEndedText}>
            The call has been ended and an issue has been created with reference number:
          </Text>
          <Text style={styles.callEndedReference}>{referenceNumber}</Text>

          <View style={styles.serviceProviderInfo}>
            <Text style={styles.serviceProviderLabel}>Service Provider Assigned:</Text>
            <Text style={styles.serviceProviderName}>
              {mockCallData.CALL_CATEGORIES.find(c => c.id === category)?.serviceProviders[0] || 'General Maintenance Technician'}
            </Text>
          </View>

          <View style={styles.callEndedButtons}>
            <TouchableOpacity
              style={[styles.callEndedButton, styles.viewIssuesButton]}
              onPress={() => navigation.navigate('AdminIssues')}
            >
              <Icon name="clipboard-list" size={20} color={COLORS.white} />
              <Text style={styles.callEndedButtonText}>View Issues</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.callEndedButton, styles.backToQueueButton]}
              onPress={() => navigation.navigate('AdminCallQueue')}
            >
              <Icon name="phone-in-talk" size={20} color={COLORS.white} />
              <Text style={styles.callEndedButtonText}>Back to Queue</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => {
              Alert.alert(
                'Leave Call',
                'Are you sure you want to leave this call? The call will remain in the queue.',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Leave',
                    style: 'destructive',
                    onPress: () => navigation.goBack()
                  },
                ]
              );
            }}
          >
            <Icon name="arrow-left" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Call in Progress</Text>
          <TouchableOpacity
            style={styles.endButton}
            onPress={() => {
              Alert.alert(
                'End Call',
                'Are you sure you want to end this call?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'End Call',
                    style: 'destructive',
                    onPress: () => setCurrentStep(mockCallData.STANDARD_QUESTIONS.length)
                  },
                ]
              );
            }}
          >
            <Icon name="phone-off" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        <View style={styles.callerInfoBar}>
          <View style={styles.callerDetails}>
            <Text style={styles.callerName}>{call?.userName || 'Unknown Caller'}</Text>
            <Text style={styles.callDuration}>
              Call Duration: {Math.floor(Math.random() * 10) + 1}:{Math.floor(Math.random() * 60).toString().padStart(2, '0')}
            </Text>
          </View>
          <View style={styles.callStatus}>
            <View style={styles.statusIndicator}>
              <Text style={styles.statusText}>In Progress</Text>
            </View>
          </View>
        </View>

        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
          {renderCurrentQuestion()}
        </ScrollView>

        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${(currentStep / (mockCallData.STANDARD_QUESTIONS.length)) * 100}%`
              }
            ]}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  endButton: {
    padding: SPACING.xs,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.darkGray,
  },
  callerInfoBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.offWhite,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  callerDetails: {
    flex: 1,
  },
  callerName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  callDuration: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  callStatus: {
    marginLeft: SPACING.md,
  },
  statusIndicator: {
    backgroundColor: '#E8F5E9',
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.success,
    fontWeight: FONT_WEIGHTS.medium,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.lg,
  },
  questionContainer: {
    marginBottom: SPACING.lg,
  },
  questionText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
    marginBottom: SPACING.md,
  },
  requiredIndicator: {
    color: COLORS.error,
    marginLeft: SPACING.xs,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
    padding: SPACING.md,
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    backgroundColor: COLORS.white,
  },
  textareaInput: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
    padding: SPACING.md,
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    backgroundColor: COLORS.white,
    height: 120,
  },
  selectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.md,
  },
  selectOption: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm,
    backgroundColor: COLORS.white,
  },
  selectedOption: {
    backgroundColor: COLORS.primaryGreen,
    borderColor: COLORS.primaryGreen,
  },
  selectOptionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  selectedOptionText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.medium,
  },
  radioContainer: {
    marginBottom: SPACING.md,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.primaryGreen,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primaryGreen,
  },
  radioOptionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 5,
  },
  prevButton: {
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
  },
  nextButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  prevButtonText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primaryGreen,
    marginLeft: SPACING.xs,
  },
  nextButtonText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    marginRight: SPACING.xs,
  },
  progressBar: {
    height: 4,
    backgroundColor: COLORS.lightGray,
    width: '100%',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primaryGreen,
  },
  summaryContainer: {
    marginBottom: SPACING.lg,
  },
  summaryTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  summarySection: {
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
  },
  summarySectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    paddingBottom: SPACING.xs,
  },
  summaryItem: {
    marginBottom: SPACING.sm,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  summaryValue: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    fontWeight: FONT_WEIGHTS.medium,
  },
  fieldLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  priorityOption: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.xs,
    borderRadius: 5,
    marginHorizontal: 2,
    alignItems: 'center',
    backgroundColor: COLORS.offWhite,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },
  priorityText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 5,
    padding: SPACING.md,
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    backgroundColor: COLORS.white,
    height: 100,
    textAlignVertical: 'top',
  },
  referenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
    backgroundColor: '#E8F5E9',
    padding: SPACING.md,
    borderRadius: 5,
  },
  referenceLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginRight: SPACING.sm,
  },
  referenceNumber: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  endCallButton: {
    backgroundColor: COLORS.error,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 5,
    marginTop: SPACING.md,
  },
  endCallButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    marginLeft: SPACING.sm,
  },
  callEndedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  callEndedTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },
  callEndedText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  callEndedReference: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.md,
  },
  serviceProviderInfo: {
    backgroundColor: '#E8F5E9',
    padding: SPACING.md,
    borderRadius: 5,
    marginBottom: SPACING.xl,
    alignItems: 'center',
    width: '100%',
  },
  serviceProviderLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  serviceProviderName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  callEndedButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  callEndedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 5,
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  viewIssuesButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  backToQueueButton: {
    backgroundColor: COLORS.info,
  },
  callEndedButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    marginLeft: SPACING.xs,
  },
});

export default AdminCallDetailsScreen;
