import { Auth } from 'aws-amplify';

// Amplify is already initialized in App.js

// Sign up a new user with ID Number
export const signUp = async (idNumber, password, name, email) => {
  try {
    console.log('Attempting to sign up with:', { idNumber, name, email });

    const { user } = await Auth.signUp({
      username: idNumber, // Use ID Number as username
      password,
      attributes: {
        email,
        name,
        'custom:idNumber': idNumber // Store ID Number as a custom attribute
      }
    });

    console.log('Sign up successful:', user);
    return { success: true, user };
  } catch (error) {
    console.error('Error signing up:', error);
    return {
      success: false,
      error: error.message || 'Failed to sign up',
      errorCode: error.code,
      errorName: error.name
    };
  }
};

// Confirm sign up with verification code
export const confirmSignUp = async (idNumber, code) => {
  try {
    console.log('Attempting to confirm sign up for ID Number:', idNumber, 'with code:', code);

    await Auth.confirmSignUp(idNumber, code);
    console.log('Confirmation successful for ID Number:', idNumber);
    return { success: true };
  } catch (error) {
    console.error('Error confirming sign up:', error);

    // Handle specific error cases
    if (error.code === 'CodeMismatchException') {
      return {
        success: false,
        error: 'Invalid verification code. Please try again.'
      };
    } else if (error.code === 'ExpiredCodeException') {
      return {
        success: false,
        error: 'Verification code has expired. Please request a new one.'
      };
    }

    return {
      success: false,
      error: error.message || 'Failed to confirm sign up',
      errorCode: error.code,
      errorName: error.name
    };
  }
};

// Sign in a user with ID Number
export const signIn = async (idNumber, password) => {
  try {
    console.log('Attempting to sign in with ID Number:', idNumber);

    const user = await Auth.signIn(idNumber, password);
    console.log('Sign in successful:', user);
    return { success: true, user };
  } catch (error) {
    console.error('Error signing in:', error);

    // Handle specific error cases
    if (error.code === 'UserNotConfirmedException') {
      return {
        success: false,
        error: 'Please confirm your account with the verification code sent to your email.',
        userConfirmationRequired: true
      };
    }

    return {
      success: false,
      error: error.message || 'Failed to sign in',
      errorCode: error.code,
      errorName: error.name
    };
  }
};

// Sign out the current user
export const signOut = async () => {
  try {
    await Auth.signOut();
    return { success: true };
  } catch (error) {
    console.error('Error signing out:', error);
    return { success: false, error: error.message || 'Failed to sign out' };
  }
};

// Get the current authenticated user
export const getCurrentUser = async () => {
  try {
    const user = await Auth.currentAuthenticatedUser();
    return { success: true, user };
  } catch (error) {
    console.error('Error getting current user:', error);
    return { success: false, error: error.message || 'No authenticated user' };
  }
};

// Resend confirmation code
export const resendVerificationCode = async (idNumber) => {
  try {
    console.log('Attempting to resend confirmation code for ID Number:', idNumber);

    await Auth.resendSignUp(idNumber);
    console.log('Confirmation code resent successfully for ID Number:', idNumber);
    return { success: true };
  } catch (error) {
    console.error('Error resending confirmation code:', error);

    return {
      success: false,
      error: error.message || 'Failed to resend confirmation code',
      errorCode: error.code,
      errorName: error.name
    };
  }
};

export default {
  signUp,
  confirmSignUp,
  signIn,
  signOut,
  getCurrentUser,
  resendVerificationCode
};
