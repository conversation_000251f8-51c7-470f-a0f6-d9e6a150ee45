import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import AsyncStorage from '@react-native-async-storage/async-storage';

const CallHistoryScreen = ({ navigation, route }) => {
  const [callHistory, setCallHistory] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load call history from storage
  useEffect(() => {
    const loadCallHistory = async () => {
      try {
        setLoading(true);
        const historyJson = await AsyncStorage.getItem('@call_history');
        if (historyJson) {
          setCallHistory(JSON.parse(historyJson));
        }
      } catch (error) {
        console.error('Error loading call history:', error);
        Alert.alert('Error', 'Failed to load call history');
      } finally {
        setLoading(false);
      }
    };

    loadCallHistory();

    // If a new call was just added, highlight it
    if (route.params?.newCall) {
      // You could add animation or highlighting logic here
    }
  }, [route.params]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed':
        return COLORS.success;
      case 'In Progress':
        return COLORS.info;
      case 'In Queue':
        return COLORS.warning;
      case 'Cancelled':
        return COLORS.error;
      default:
        return COLORS.darkGray;
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed':
        return 'check-circle';
      case 'In Progress':
        return 'phone-in-talk';
      case 'In Queue':
        return 'clock-outline';
      case 'Cancelled':
        return 'close-circle';
      default:
        return 'information-outline';
    }
  };

  // Clear call history
  const handleClearHistory = () => {
    Alert.alert(
      'Clear History',
      'Are you sure you want to clear your call history?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear', 
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('@call_history');
              setCallHistory([]);
              Alert.alert('Success', 'Call history cleared');
            } catch (error) {
              console.error('Error clearing call history:', error);
              Alert.alert('Error', 'Failed to clear call history');
            }
          }
        },
      ]
    );
  };

  // Render call history item
  const renderCallItem = ({ item }) => (
    <View style={styles.callItem}>
      <View style={styles.callHeader}>
        <View style={styles.statusContainer}>
          <Icon 
            name={getStatusIcon(item.status)} 
            size={20} 
            color={getStatusColor(item.status)} 
          />
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status}
          </Text>
        </View>
        <Text style={styles.dateText}>{formatDate(item.date)}</Text>
      </View>
      
      <View style={styles.callDetails}>
        <Text style={styles.referenceLabel}>Reference Number:</Text>
        <Text style={styles.referenceNumber}>{item.referenceNumber}</Text>
      </View>
      
      {item.duration !== '-' && (
        <View style={styles.callDetails}>
          <Text style={styles.durationLabel}>Call Duration:</Text>
          <Text style={styles.durationText}>{item.duration}</Text>
        </View>
      )}
      
      <TouchableOpacity 
        style={styles.detailsButton}
        onPress={() => Alert.alert(
          'Call Details',
          `Reference: ${item.referenceNumber}\nDate: ${formatDate(item.date)}\nStatus: ${item.status}\nDuration: ${item.duration}`
        )}
      >
        <Text style={styles.detailsButtonText}>View Details</Text>
        <Icon name="chevron-right" size={16} color={COLORS.primaryGreen} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.primaryGreen} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Call History</Text>
        {callHistory.length > 0 && (
          <TouchableOpacity 
            style={styles.clearButton}
            onPress={handleClearHistory}
          >
            <Icon name="delete-outline" size={24} color={COLORS.primaryGreen} />
          </TouchableOpacity>
        )}
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading call history...</Text>
        </View>
      ) : callHistory.length > 0 ? (
        <FlatList
          data={callHistory}
          renderItem={renderCallItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Icon name="phone-off" size={60} color={COLORS.lightGray} />
          <Text style={styles.emptyText}>No call history yet</Text>
          <Text style={styles.emptySubtext}>
            Your calls to the Presidential Hotline will appear here
          </Text>
          <TouchableOpacity 
            style={styles.makeCallButton}
            onPress={() => navigation.navigate('Call')}
          >
            <Icon name="phone" size={20} color={COLORS.white} />
            <Text style={styles.makeCallButtonText}>Make a Call</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  listContainer: {
    padding: SPACING.md,
  },
  callItem: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  callHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: SPACING.xs,
  },
  dateText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  callDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  referenceLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    width: 130,
  },
  referenceNumber: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
    flex: 1,
  },
  durationLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    width: 130,
  },
  durationText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.black,
    flex: 1,
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: SPACING.sm,
  },
  detailsButtonText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
    marginRight: SPACING.xs,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginTop: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  makeCallButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primaryGreen,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: 30,
  },
  makeCallButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: SPACING.xs,
  },
});

export default CallHistoryScreen;
