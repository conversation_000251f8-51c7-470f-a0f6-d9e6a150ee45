import React from 'react';
import { View, StyleSheet } from 'react-native';
import { COLORS } from '../theme/colors';

/**
 * A simplified skeleton loader component without animations
 * to avoid NaN transform issues
 *
 * @param {Object} props - Component props
 * @param {number|string} props.width - Width of the skeleton
 * @param {number} props.height - Height of the skeleton
 * @param {number} props.borderRadius - Border radius of the skeleton
 * @param {Object} props.style - Additional styles to apply
 */
const SkeletonLoader = ({
  width = 100,
  height = 20,
  borderRadius = 4,
  style
}) => {
  // Simple static skeleton without animations
  return (
    <View
      style={[
        styles.container,
        { width, height, borderRadius },
        style,
      ]}
    >
      <View style={styles.shimmer} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.lightGray,
    overflow: 'hidden',
  },
  shimmer: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
});

export default SkeletonLoader;
