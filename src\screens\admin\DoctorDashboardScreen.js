import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { useMockAuth } from '../../context/MockAuthContext';
import {
  initializeMockDoctorData,
  getDoctorAppointments,
  sendPrescriptionReminder,
  MOCK_PRESCRIPTIONS
} from '../../data/mockDoctorData';

const DoctorDashboardScreen = ({ navigation }) => {
  const { currentUser, signOut } = useMockAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [todayAppointments, setTodayAppointments] = useState([]);
  const [pendingPrescriptions, setPendingPrescriptions] = useState([]);
  const [stats, setStats] = useState({
    totalAppointmentsToday: 0,
    completedAppointments: 0,
    pendingPrescriptions: 0,
    upcomingAppointments: 0,
  });

  // Initialize data and load appointments
  useEffect(() => {
    const setupData = async () => {
      await initializeMockDoctorData();
      await loadDashboardData();
      setLoading(false);
    };

    setupData();
  }, [currentUser]);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      // Get today's appointments
      const appointments = await getDoctorAppointments(currentUser?.id, new Date());
      setTodayAppointments(appointments);

      // Get pending prescriptions (not dispensed)
      const pendingPrescriptions = MOCK_PRESCRIPTIONS.filter(
        prescription => !prescription.dispensed && prescription.doctorId === currentUser?.id
      );
      setPendingPrescriptions(pendingPrescriptions);

      // Update stats
      setStats({
        totalAppointmentsToday: appointments.length,
        completedAppointments: appointments.filter(app => app.status === 'completed').length,
        pendingPrescriptions: pendingPrescriptions.length,
        upcomingAppointments: appointments.filter(app => {
          const appTime = new Date(`2000/01/01 ${app.time}`);
          const now = new Date();
          const currentTime = new Date(`2000/01/01 ${now.getHours()}:${now.getMinutes()}`);
          return appTime > currentTime && app.status !== 'completed';
        }).length,
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data. Please try again.');
    }
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      const result = await signOut();
      if (result.success) {
        // Explicitly navigate to Login screen
        navigation.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        });
      } else {
        Alert.alert('Logout Failed', 'Could not log out. Please try again.');
      }
    } catch (error) {
      console.error('Error during logout:', error);
      Alert.alert('Error', 'An unexpected error occurred during logout.');
    }
  };

  // Handle send prescription reminder
  const handleSendReminder = async (prescription) => {
    try {
      const result = await sendPrescriptionReminder(prescription.id);
      if (result) {
        Alert.alert(
          'Reminder Sent',
          `A reminder has been sent to ${prescription.patientName} to collect their prescription.`
        );
        await loadDashboardData();
      } else {
        Alert.alert('Error', 'Failed to send reminder. Please try again.');
      }
    } catch (error) {
      console.error('Error sending reminder:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  // Render stat card
  const renderStatCard = (title, value, iconName, color, onPress) => (
    <TouchableOpacity
      style={styles.statCard}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={[styles.statIconContainer, { backgroundColor: color }]}>
        <Icon name={iconName} size={24} color={COLORS.white} />
      </View>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statTitle}>{title}</Text>
    </TouchableOpacity>
  );

  // Render appointment item
  const renderAppointmentItem = ({ item }) => (
    <TouchableOpacity
      style={styles.appointmentItem}
      onPress={() => navigation.navigate('DoctorPatientDetails', { patientId: item.patientId })}
    >
      <View style={styles.appointmentTimeContainer}>
        <Text style={styles.appointmentTime}>{item.time}</Text>
        <Text style={styles.appointmentDuration}>{item.duration} min</Text>
      </View>
      <View style={styles.appointmentDetails}>
        <Text style={styles.patientName}>{item.patientName}</Text>
        <Text style={styles.appointmentReason}>{item.reason}</Text>
        {item.notes && (
          <Text style={styles.appointmentNotes} numberOfLines={1}>
            Note: {item.notes}
          </Text>
        )}
      </View>
      <View style={styles.appointmentStatus}>
        <View style={[
          styles.statusIndicator,
          item.status === 'completed' ? styles.completedStatus : styles.scheduledStatus
        ]} />
        <Text style={styles.statusText}>
          {item.status === 'completed' ? 'Completed' : 'Scheduled'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  // Render prescription item
  const renderPrescriptionItem = ({ item }) => (
    <View style={styles.prescriptionItem}>
      <View style={styles.prescriptionHeader}>
        <Text style={styles.prescriptionPatient}>{item.patientName}</Text>
        <Text style={styles.prescriptionDate}>{item.date}</Text>
      </View>
      <View style={styles.prescriptionMeds}>
        {item.medications.map((med, index) => (
          <Text key={index} style={styles.medicationText}>
            • {med.name} {med.dosage} - {med.frequency}
          </Text>
        ))}
      </View>
      <View style={styles.prescriptionActions}>
        <TouchableOpacity
          style={[
            styles.prescriptionAction,
            item.collectionReminded && styles.disabledAction
          ]}
          onPress={() => handleSendReminder(item)}
          disabled={item.collectionReminded}
        >
          <Icon
            name="bell-ring-outline"
            size={20}
            color={item.collectionReminded ? COLORS.gray : COLORS.primaryGreen}
          />
          <Text
            style={[
              styles.actionText,
              item.collectionReminded && styles.disabledActionText
            ]}
          >
            {item.collectionReminded ? 'Reminder Sent' : 'Send Reminder'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.prescriptionAction}
          onPress={() => navigation.navigate('DoctorPatientDetails', { patientId: item.patientId })}
        >
          <Icon name="account" size={20} color={COLORS.primaryGreen} />
          <Text style={styles.actionText}>Patient Details</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>Doctor Dashboard</Text>
          <Text style={styles.headerSubtitle}>
            Welcome, {currentUser?.name || 'Doctor'}
          </Text>
        </View>
        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <Icon name="logout" size={24} color={COLORS.primaryGreen} />
        </TouchableOpacity>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          {renderStatCard(
            'Today\'s Appointments',
            stats.totalAppointmentsToday,
            'calendar-clock',
            COLORS.primaryGreen,
            () => navigation.navigate('DoctorAppointments')
          )}
          {renderStatCard(
            'Completed',
            stats.completedAppointments,
            'check-circle',
            COLORS.success,
            () => navigation.navigate('DoctorAppointments', { filter: 'completed' })
          )}
          {renderStatCard(
            'Upcoming',
            stats.upcomingAppointments,
            'clock-outline',
            COLORS.info,
            () => navigation.navigate('DoctorAppointments', { filter: 'upcoming' })
          )}
          {renderStatCard(
            'Pending Prescriptions',
            stats.pendingPrescriptions,
            'pill',
            COLORS.warning,
            () => navigation.navigate('DoctorPrescription')
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsContainer}>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('DoctorAppointments')}
            >
              <Icon name="calendar" size={24} color={COLORS.white} />
              <Text style={styles.quickActionText}>Appointments</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('DoctorPrescription')}
            >
              <Icon name="prescription" size={24} color={COLORS.white} />
              <Text style={styles.quickActionText}>Prescriptions</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('DoctorReports')}
            >
              <Icon name="chart-line" size={24} color={COLORS.white} />
              <Text style={styles.quickActionText}>Reports</Text>
            </TouchableOpacity>
          </View>

          {/* Emergency Services Button */}
          <TouchableOpacity
            style={styles.emergencyButton}
            onPress={() => Alert.alert(
              'Emergency Services',
              'This would dial emergency services in a real app.\n\nEmergency Number: 10177',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Call Now', style: 'destructive' }
              ]
            )}
          >
            <Icon name="phone-alert" size={24} color={COLORS.white} />
            <Text style={styles.emergencyButtonText}>Call Emergency Services</Text>
          </TouchableOpacity>
        </View>

        {/* Today's Appointments */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Appointments</Text>
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('DoctorAppointments')}
            >
              <Text style={styles.viewAllText}>View All</Text>
              <Icon name="chevron-right" size={16} color={COLORS.primaryGreen} />
            </TouchableOpacity>
          </View>

          {todayAppointments.length > 0 ? (
            <FlatList
              data={todayAppointments.slice(0, 3)}
              renderItem={renderAppointmentItem}
              keyExtractor={item => item.id}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyStateContainer}>
              <Icon name="calendar-blank" size={40} color={COLORS.lightGray} />
              <Text style={styles.emptyStateText}>No appointments scheduled for today</Text>
            </View>
          )}
        </View>

        {/* Pending Prescriptions */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Pending Prescriptions</Text>
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('DoctorPrescription')}
            >
              <Text style={styles.viewAllText}>View All</Text>
              <Icon name="chevron-right" size={16} color={COLORS.primaryGreen} />
            </TouchableOpacity>
          </View>

          {pendingPrescriptions.length > 0 ? (
            <FlatList
              data={pendingPrescriptions.slice(0, 3)}
              renderItem={renderPrescriptionItem}
              keyExtractor={item => item.id}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyStateContainer}>
              <Icon name="pill-off" size={40} color={COLORS.lightGray} />
              <Text style={styles.emptyStateText}>No pending prescriptions</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  logoutButton: {
    padding: SPACING.sm,
  },
  scrollContent: {
    padding: SPACING.md,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  statCard: {
    width: '48%',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    alignItems: 'center',
    // Shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  statIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statValue: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  statTitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    textAlign: 'center',
  },
  sectionContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginRight: 2,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 10,
    padding: SPACING.md,
    alignItems: 'center',
    width: '30%',
  },
  quickActionText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  emergencyButton: {
    backgroundColor: COLORS.error,
    borderRadius: 10,
    padding: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    marginTop: SPACING.md,
    // Enhanced shadow for emergency button
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  emergencyButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    marginLeft: SPACING.sm,
  },
  appointmentItem: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    paddingVertical: SPACING.md,
  },
  appointmentTimeContainer: {
    width: 70,
    alignItems: 'center',
  },
  appointmentTime: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  appointmentDuration: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  appointmentDetails: {
    flex: 1,
    paddingHorizontal: SPACING.sm,
  },
  patientName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
    marginBottom: 2,
  },
  appointmentReason: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  appointmentNotes: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
    fontStyle: 'italic',
  },
  appointmentStatus: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginBottom: 4,
  },
  scheduledStatus: {
    backgroundColor: COLORS.info,
  },
  completedStatus: {
    backgroundColor: COLORS.success,
  },
  statusText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  prescriptionItem: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    paddingVertical: SPACING.md,
  },
  prescriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  prescriptionPatient: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
  },
  prescriptionDate: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  prescriptionMeds: {
    marginBottom: SPACING.sm,
  },
  medicationText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  prescriptionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  prescriptionAction: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.xs,
  },
  actionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginLeft: 4,
  },
  disabledAction: {
    opacity: 0.6,
  },
  disabledActionText: {
    color: COLORS.gray,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyStateText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
    marginTop: SPACING.sm,
    textAlign: 'center',
  },
});

export default DoctorDashboardScreen;
