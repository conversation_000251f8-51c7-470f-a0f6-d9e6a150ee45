import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

// Mock data for community announcements
const ANNOUNCEMENTS = [
  {
    id: '1',
    title: 'Community Clean-up Day',
    content: 'Join us for a community clean-up day on June 15th. Meet at the town hall at 9:00 AM. Refreshments will be provided.',
    date: '2023-06-05',
    author: 'Community Manager',
    category: 'event',
  },
  {
    id: '2',
    title: 'Water Interruption Notice',
    content: 'There will be a scheduled water interruption on June 10th from 8:00 AM to 2:00 PM due to maintenance work.',
    date: '2023-06-03',
    author: 'Water Department',
    category: 'notice',
  },
  {
    id: '3',
    title: 'New Recycling Program',
    content: 'A new recycling program will be launched on July 1st. Collection bins will be distributed to all households by June 25th.',
    date: '2023-06-01',
    author: 'Environmental Services',
    category: 'program',
  },
];

// Mock data for upcoming events
const EVENTS = [
  {
    id: '1',
    title: 'Community Clean-up Day',
    date: '2023-06-15',
    time: '09:00 - 13:00',
    location: 'Town Hall',
    category: 'environment',
  },
  {
    id: '2',
    title: 'Youth Skills Workshop',
    date: '2023-06-20',
    time: '14:00 - 17:00',
    location: 'Community Center',
    category: 'education',
  },
  {
    id: '3',
    title: 'Health Awareness Day',
    date: '2023-06-25',
    time: '10:00 - 15:00',
    location: 'Public Library',
    category: 'health',
  },
];

// Mock data for discussion forums
const FORUMS = [
  {
    id: '1',
    title: 'Local Business Development',
    description: 'Discuss ideas for supporting local businesses and entrepreneurs.',
    members: 156,
    posts: 42,
    lastActive: '2023-06-04',
  },
  {
    id: '2',
    title: 'Community Safety',
    description: 'Share information and tips about keeping our community safe.',
    members: 203,
    posts: 78,
    lastActive: '2023-06-05',
  },
  {
    id: '3',
    title: 'Education Initiatives',
    description: 'Discuss educational programs and opportunities for all ages.',
    members: 124,
    posts: 31,
    lastActive: '2023-06-02',
  },
];

const CommunityScreen = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('announcements');

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get category color
  const getCategoryColor = (category) => {
    switch (category) {
      case 'event':
      case 'environment':
        return COLORS.success;
      case 'notice':
      case 'health':
        return COLORS.warning;
      case 'program':
      case 'education':
        return COLORS.info;
      default:
        return COLORS.primaryGreen;
    }
  };

  // Get category label
  const getCategoryLabel = (category) => {
    switch (category) {
      case 'event':
        return 'Event';
      case 'notice':
        return 'Notice';
      case 'program':
        return 'Program';
      case 'environment':
        return 'Environment';
      case 'education':
        return 'Education';
      case 'health':
        return 'Health';
      default:
        return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  // Render announcement item
  const renderAnnouncementItem = ({ item }) => (
    <TouchableOpacity
      style={styles.announcementItem}
      onPress={() => alert(`${item.title} details coming soon!`)}
    >
      <View style={styles.announcementHeader}>
        <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(item.category) }]}>
          <Text style={styles.categoryText}>{getCategoryLabel(item.category)}</Text>
        </View>
        <Text style={styles.announcementDate}>{formatDate(item.date)}</Text>
      </View>
      <Text style={styles.announcementTitle}>{item.title}</Text>
      <Text style={styles.announcementContent} numberOfLines={3}>
        {item.content}
      </Text>
      <View style={styles.announcementFooter}>
        <Text style={styles.announcementAuthor}>Posted by: {item.author}</Text>
        <Text style={styles.readMoreText}>Read More</Text>
      </View>
    </TouchableOpacity>
  );

  // Render event item
  const renderEventItem = ({ item }) => (
    <TouchableOpacity
      style={styles.eventItem}
      onPress={() => alert(`${item.title} details coming soon!`)}
    >
      <View style={[styles.eventCategoryIndicator, { backgroundColor: getCategoryColor(item.category) }]} />
      <View style={styles.eventContent}>
        <Text style={styles.eventTitle}>{item.title}</Text>
        <View style={styles.eventDetails}>
          <Text style={styles.eventDetailText}>📅 {formatDate(item.date)}</Text>
          <Text style={styles.eventDetailText}>⏰ {item.time}</Text>
          <Text style={styles.eventDetailText}>📍 {item.location}</Text>
        </View>
        <View style={styles.eventActions}>
          <CustomButton
            title="RSVP"
            onPress={() => alert(`RSVP for ${item.title} coming soon!`)}
            style={styles.rsvpButton}
            textStyle={styles.rsvpButtonText}
          />
          <CustomButton
            title="Add to Calendar"
            onPress={() => alert(`Calendar feature coming soon!`)}
            style={styles.calendarButton}
            textStyle={styles.calendarButtonText}
            type="outline"
          />
        </View>
      </View>
    </TouchableOpacity>
  );

  // Render forum item
  const renderForumItem = ({ item }) => (
    <TouchableOpacity
      style={styles.forumItem}
      onPress={() => alert(`${item.title} forum coming soon!`)}
    >
      <View style={styles.forumContent}>
        <Text style={styles.forumTitle}>{item.title}</Text>
        <Text style={styles.forumDescription}>{item.description}</Text>
        <View style={styles.forumStats}>
          <Text style={styles.forumStatText}>👥 {item.members} members</Text>
          <Text style={styles.forumStatText}>💬 {item.posts} posts</Text>
          <Text style={styles.forumStatText}>🕒 Last active: {formatDate(item.lastActive)}</Text>
        </View>
      </View>
      <Text style={styles.forumArrow}>›</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Community Hub</Text>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'announcements' && styles.activeTab]}
          onPress={() => setActiveTab('announcements')}
        >
          <Text style={[styles.tabText, activeTab === 'announcements' && styles.activeTabText]}>
            Announcements
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'events' && styles.activeTab]}
          onPress={() => setActiveTab('events')}
        >
          <Text style={[styles.tabText, activeTab === 'events' && styles.activeTabText]}>
            Events
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'forums' && styles.activeTab]}
          onPress={() => setActiveTab('forums')}
        >
          <Text style={[styles.tabText, activeTab === 'forums' && styles.activeTabText]}>
            Forums
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Announcements Tab */}
        {activeTab === 'announcements' && (
          <View style={styles.announcementsContainer}>
            <FlatList
              data={ANNOUNCEMENTS}
              renderItem={renderAnnouncementItem}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              contentContainerStyle={styles.announcementsList}
            />
          </View>
        )}

        {/* Events Tab */}
        {activeTab === 'events' && (
          <View style={styles.eventsContainer}>
            <FlatList
              data={EVENTS}
              renderItem={renderEventItem}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              contentContainerStyle={styles.eventsList}
            />
          </View>
        )}

        {/* Forums Tab */}
        {activeTab === 'forums' && (
          <View style={styles.forumsContainer}>
            <FlatList
              data={FORUMS}
              renderItem={renderForumItem}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              contentContainerStyle={styles.forumsList}
            />
            <CustomButton
              title="Create New Discussion"
              onPress={() => alert('Create discussion feature coming soon!')}
              style={styles.createDiscussionButton}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.md,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: COLORS.primaryGreen,
  },
  tabText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
  },
  activeTabText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
  },
  scrollView: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra padding at bottom for tab bar
  },
  announcementsContainer: {
    marginBottom: SPACING.md,
  },
  announcementsList: {
    paddingBottom: SPACING.xs,
  },
  announcementItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
  },
  announcementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  categoryBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 5,
  },
  categoryText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
  },
  announcementDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  announcementTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  announcementContent: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.md,
  },
  announcementFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  announcementAuthor: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  readMoreText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
  },
  eventsContainer: {
    marginBottom: SPACING.md,
  },
  eventsList: {
    paddingBottom: SPACING.xs,
  },
  eventItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    overflow: 'hidden',
    elevation: 2,
  },
  eventCategoryIndicator: {
    width: 8,
  },
  eventContent: {
    flex: 1,
    padding: SPACING.md,
  },
  eventTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  eventDetails: {
    marginBottom: SPACING.md,
  },
  eventDetailText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  eventActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rsvpButton: {
    flex: 1,
    marginRight: SPACING.sm,
    height: 40,
  },
  rsvpButtonText: {
    fontSize: FONT_SIZES.sm,
  },
  calendarButton: {
    flex: 1,
    height: 40,
  },
  calendarButtonText: {
    fontSize: FONT_SIZES.sm,
  },
  forumsContainer: {
    marginBottom: SPACING.md,
  },
  forumsList: {
    paddingBottom: SPACING.md,
  },
  forumItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
  },
  forumContent: {
    flex: 1,
  },
  forumTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  forumDescription: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.sm,
  },
  forumStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  forumStatText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginRight: SPACING.md,
    marginBottom: SPACING.xs,
  },
  forumArrow: {
    fontSize: FONT_SIZES.xxl,
    color: COLORS.gray,
    marginLeft: SPACING.sm,
  },
  createDiscussionButton: {
    marginTop: SPACING.sm,
  },
});

export default CommunityScreen;
