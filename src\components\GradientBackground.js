import React from 'react';
import { View, StyleSheet } from 'react-native';

/**
 * A simple gradient background component that simulates a linear gradient
 * using multiple stacked views with varying opacity.
 * 
 * @param {Object} props - Component props
 * @param {Array} props.colors - Array of colors to use in the gradient
 * @param {string} props.direction - Direction of the gradient ('vertical' or 'horizontal')
 * @param {Object} props.style - Additional styles to apply to the container
 * @param {React.ReactNode} props.children - Child components
 */
const GradientBackground = ({ 
  colors = ['#2E7D32', '#1B5E20'], 
  direction = 'vertical',
  style,
  children 
}) => {
  const isVertical = direction === 'vertical';
  
  return (
    <View style={[styles.container, style]}>
      {colors.map((color, index) => {
        const position = index / (colors.length - 1);
        
        return (
          <View
            key={`gradient-layer-${index}`}
            style={[
              styles.gradientLayer,
              {
                backgroundColor: color,
                opacity: 1 - position,
                ...(isVertical
                  ? { top: `${position * 100}%` }
                  : { left: `${position * 100}%` }),
              },
            ]}
          />
        );
      })}
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  gradientLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default GradientBackground;
