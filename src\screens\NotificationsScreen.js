import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import {
  getNotifications,
  markNotificationAsRead
} from '../data/mockHealthData';

const NotificationsScreen = ({ navigation }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeFilter, setActiveFilter] = useState('all');

  // Load notifications on component mount
  useEffect(() => {
    loadNotifications();
  }, []);

  // Load notifications from storage
  const loadNotifications = async () => {
    setLoading(true);
    try {
      const notificationsData = await getNotifications();
      // Sort by date (newest first)
      const sortedNotifications = notificationsData.sort((a, b) =>
        new Date(b.date) - new Date(a.date)
      );
      setNotifications(sortedNotifications);
    } catch (error) {
      console.error('Error loading notifications:', error);
      Alert.alert('Error', 'Failed to load notifications. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle notification press
  const handleNotificationPress = async (notification) => {
    try {
      // Mark notification as read
      if (!notification.read) {
        await markNotificationAsRead(notification.id);
        // Update local state
        setNotifications(prevNotifications =>
          prevNotifications.map(n =>
            n.id === notification.id ? { ...n, read: true } : n
          )
        );
      }

      // Navigate based on notification type
      switch (notification.type) {
        case 'appointment':
          // In a real app, navigate to appointment details
          Alert.alert('Appointment', notification.message);
          break;
        case 'medication':
          // In a real app, navigate to medication details
          Alert.alert('Medication', notification.message);
          break;
        case 'results':
          // In a real app, navigate to test results
          Alert.alert('Test Results', notification.message);
          break;
        case 'vaccination':
          // In a real app, navigate to vaccination details
          Alert.alert('Vaccination', notification.message);
          break;
        default:
          Alert.alert('Notification', notification.message);
      }
    } catch (error) {
      console.error('Error handling notification:', error);
      Alert.alert('Error', 'Failed to process notification. Please try again.');
    }
  };

  // Filter notifications
  const filteredNotifications = activeFilter === 'all'
    ? notifications
    : notifications.filter(notification => notification.type === activeFilter);

  // Get icon for notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'appointment':
        return 'calendar-clock';
      case 'medication':
        return 'pill';
      case 'results':
        return 'file-document-outline';
      case 'vaccination':
        return 'needle';
      default:
        return 'bell-outline';
    }
  };

  // Get color for notification type
  const getNotificationColor = (type) => {
    switch (type) {
      case 'appointment':
        return COLORS.primaryGreen;
      case 'medication':
        return COLORS.info;
      case 'results':
        return COLORS.warning;
      case 'vaccination':
        return COLORS.success;
      default:
        return COLORS.gray;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Today - show time
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 1) {
      // Yesterday
      return 'Yesterday';
    } else if (diffDays < 7) {
      // Within a week
      return `${diffDays} days ago`;
    } else {
      // More than a week
      return date.toLocaleDateString();
    }
  };

  // Render notification item
  const renderNotificationItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        !item.read && styles.unreadNotification
      ]}
      onPress={() => handleNotificationPress(item)}
    >
      <View
        style={[
          styles.notificationIconContainer,
          { backgroundColor: getNotificationColor(item.type) }
        ]}
      >
        <Icon
          name={getNotificationIcon(item.type)}
          size={24}
          color={COLORS.white}
        />
      </View>
      <View style={styles.notificationContent}>
        <Text style={styles.notificationTitle}>{item.title}</Text>
        <Text style={styles.notificationMessage} numberOfLines={2}>
          {item.message}
        </Text>
        <Text style={styles.notificationDate}>{formatDate(item.date)}</Text>
      </View>
      {!item.read && <View style={styles.unreadIndicator} />}
    </TouchableOpacity>
  );

  // Render filter button
  const renderFilterButton = (filter, label, icon) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        activeFilter === filter && styles.activeFilterButton
      ]}
      onPress={() => setActiveFilter(filter)}
    >
      <Icon
        name={icon}
        size={18}
        color={activeFilter === filter ? COLORS.white : COLORS.darkGray}
      />
      <Text
        style={[
          styles.filterButtonText,
          activeFilter === filter && styles.activeFilterButtonText
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {renderFilterButton('all', 'All', 'bell-outline')}
          {renderFilterButton('appointment', 'Appointments', 'calendar-clock')}
          {renderFilterButton('medication', 'Medication', 'pill')}
          {renderFilterButton('results', 'Test Results', 'file-document-outline')}
          {renderFilterButton('vaccination', 'Vaccinations', 'needle')}
        </ScrollView>
      </View>

      {/* Notifications List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primaryGreen} />
          <Text style={styles.loadingText}>Loading notifications...</Text>
        </View>
      ) : filteredNotifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="bell-off-outline" size={64} color={COLORS.lightGray} />
          <Text style={styles.emptyText}>No notifications found</Text>
          <Text style={styles.emptySubtext}>
            {activeFilter === 'all'
              ? 'You don\'t have any notifications yet.'
              : `You don't have any ${activeFilter} notifications.`}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredNotifications}
          renderItem={renderNotificationItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.notificationsList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    marginRight: SPACING.sm,
    borderRadius: 20,
    backgroundColor: COLORS.offWhite,
  },
  activeFilterButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  filterButtonText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: SPACING.xs,
  },
  activeFilterButtonText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  notificationsList: {
    padding: SPACING.md,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    // Shadow for Android
    elevation: 2,
  },
  unreadNotification: {
    backgroundColor: COLORS.offWhite,
  },
  notificationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primaryGreen,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  notificationMessage: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  notificationDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
  },
  unreadIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primaryGreen,
    marginLeft: SPACING.sm,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },
});

export default NotificationsScreen;
