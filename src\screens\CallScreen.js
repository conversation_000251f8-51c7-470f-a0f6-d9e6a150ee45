import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  ScrollView,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import { IMAGES } from '../assets/images';
import AsyncStorage from '@react-native-async-storage/async-storage';

const CallScreen = ({ navigation }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [queuePosition, setQueuePosition] = useState(0);
  const [callInProgress, setCallInProgress] = useState(false);
  const [referenceNumber, setReferenceNumber] = useState('');

  // Function to generate a random reference number
  const generateReferenceNumber = () => {
    const prefix = 'LT';
    const timestamp = new Date().getTime().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}-${timestamp}-${random}`;
  };

  // Function to save call to history
  const saveCallToHistory = async (refNumber, status) => {
    try {
      // Get existing call history
      const historyJson = await AsyncStorage.getItem('@call_history');
      let history = historyJson ? JSON.parse(historyJson) : [];
      
      // Add new call to history
      const newCall = {
        id: Date.now().toString(),
        referenceNumber: refNumber,
        date: new Date().toISOString(),
        status: status,
        duration: status === 'Completed' ? Math.floor(Math.random() * 15) + 1 + ' min' : '-',
      };
      
      history = [newCall, ...history];
      
      // Save updated history
      await AsyncStorage.setItem('@call_history', JSON.stringify(history));
      
      return newCall;
    } catch (error) {
      console.error('Error saving call to history:', error);
      return null;
    }
  };

  // Function to simulate SMS notification
  const simulateSmsNotification = (refNumber) => {
    Alert.alert(
      'SMS Notification Sent',
      `An SMS has been sent to your registered mobile number with reference number: ${refNumber}`,
      [{ text: 'OK' }]
    );
  };

  // Function to handle call initiation
  const handleCallInitiate = () => {
    setModalVisible(true);
  };

  // Function to confirm call
  const handleConfirmCall = () => {
    setModalVisible(false);
    
    // Generate a random queue position (1-10)
    const randomQueue = Math.floor(Math.random() * 10) + 1;
    setQueuePosition(randomQueue);
    setCallInProgress(true);
    
    // Generate reference number
    const refNumber = generateReferenceNumber();
    setReferenceNumber(refNumber);
    
    // Save call to history as "In Queue"
    saveCallToHistory(refNumber, 'In Queue');
    
    // Simulate SMS notification
    simulateSmsNotification(refNumber);
    
    // Simulate call connecting after queue
    setTimeout(() => {
      setQueuePosition(0);
      Alert.alert(
        'Call Connected',
        'You are now connected to a presidential hotline agent.',
        [{ text: 'OK' }]
      );
      
      // Update call status to "In Progress"
      saveCallToHistory(refNumber, 'In Progress');
      
      // Simulate call ending after random time (5-15 seconds for demo)
      setTimeout(() => {
        setCallInProgress(false);
        Alert.alert(
          'Call Ended',
          'Thank you for calling the presidential hotline. Your feedback has been recorded.',
          [
            { 
              text: 'View History', 
              onPress: () => navigation.navigate('CallHistory', { newCall: true }) 
            },
            { text: 'OK' }
          ]
        );
        
        // Update call status to "Completed"
        saveCallToHistory(refNumber, 'Completed');
      }, Math.floor(Math.random() * 10000) + 5000);
    }, randomQueue * 1000);
  };

  // Function to cancel call
  const handleCancelCall = () => {
    if (callInProgress) {
      // Save call to history as "Cancelled"
      saveCallToHistory(referenceNumber, 'Cancelled');
    }
    
    setModalVisible(false);
    setCallInProgress(false);
    setQueuePosition(0);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollView}>
        {/* Header with Logo */}
        <View style={styles.header}>
          <Image source={IMAGES.logo} style={styles.logo} resizeMode="contain" />
          <Text style={styles.headerTitle}>Presidential Hotline</Text>
        </View>

        {/* Hotline Information */}
        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>Direct Line to Government</Text>
          <Text style={styles.infoText}>
            The Presidential Hotline is your direct channel to report service delivery issues, 
            provide feedback, or raise concerns about government services.
          </Text>
          
          <View style={styles.bulletContainer}>
            <Icon name="information-outline" size={20} color={COLORS.primaryGreen} />
            <Text style={styles.bulletText}>Free data call (no airtime needed)</Text>
          </View>
          
          <View style={styles.bulletContainer}>
            <Icon name="clock-outline" size={20} color={COLORS.primaryGreen} />
            <Text style={styles.bulletText}>Available Monday to Friday, 8:00 AM - 5:00 PM</Text>
          </View>
          
          <View style={styles.bulletContainer}>
            <Icon name="account-voice" size={20} color={COLORS.primaryGreen} />
            <Text style={styles.bulletText}>Speak to a trained government agent</Text>
          </View>
          
          <View style={styles.bulletContainer}>
            <Icon name="translate" size={20} color={COLORS.primaryGreen} />
            <Text style={styles.bulletText}>Service available in all official languages</Text>
          </View>
        </View>

        {/* Call Button */}
        <TouchableOpacity 
          style={[
            styles.callButton, 
            callInProgress && styles.callButtonDisabled
          ]} 
          onPress={handleCallInitiate}
          disabled={callInProgress}
        >
          <Icon name="phone" size={30} color={COLORS.white} />
          <Text style={styles.callButtonText}>
            {callInProgress ? 'Call in Progress' : 'Call Presidential Hotline'}
          </Text>
        </TouchableOpacity>

        {/* Queue Information */}
        {queuePosition > 0 && (
          <View style={styles.queueContainer}>
            <Icon name="account-multiple" size={24} color={COLORS.primaryGreen} />
            <Text style={styles.queueText}>
              You are number {queuePosition} in the queue
            </Text>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={handleCancelCall}
            >
              <Text style={styles.cancelButtonText}>Cancel Call</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Reference Number Display */}
        {referenceNumber && callInProgress && (
          <View style={styles.referenceContainer}>
            <Text style={styles.referenceLabel}>Your Reference Number:</Text>
            <Text style={styles.referenceNumber}>{referenceNumber}</Text>
            <Text style={styles.referenceSaved}>
              This number has been sent to your phone via SMS and saved in your call history.
            </Text>
          </View>
        )}

        {/* Call History Button */}
        <TouchableOpacity 
          style={styles.historyButton}
          onPress={() => navigation.navigate('CallHistory')}
        >
          <Icon name="history" size={20} color={COLORS.primaryGreen} />
          <Text style={styles.historyButtonText}>View Call History</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Confirmation Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Icon name="phone-in-talk" size={50} color={COLORS.primaryGreen} />
            <Text style={styles.modalTitle}>Confirm Call</Text>
            <Text style={styles.modalText}>
              You are about to place a data call to the Presidential Hotline.
              This will not use your airtime but requires a data connection.
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelModalButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelModalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.modalButton, styles.confirmModalButton]}
                onPress={handleConfirmCall}
              >
                <Text style={styles.confirmModalButtonText}>Call Now</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  scrollView: {
    flexGrow: 1,
    padding: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    textAlign: 'center',
  },
  infoCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
  },
  infoText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.lg,
    lineHeight: 22,
  },
  bulletContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  bulletText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginLeft: SPACING.sm,
    flex: 1,
  },
  callButton: {
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.lg,
    marginVertical: SPACING.lg,
  },
  callButtonDisabled: {
    backgroundColor: COLORS.lightGray,
  },
  callButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    marginLeft: SPACING.md,
  },
  queueContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  queueText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.black,
    fontWeight: FONT_WEIGHTS.medium,
    marginVertical: SPACING.sm,
  },
  cancelButton: {
    marginTop: SPACING.sm,
    padding: SPACING.sm,
  },
  cancelButtonText: {
    color: COLORS.error,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
  },
  referenceContainer: {
    backgroundColor: '#E8F5E9',
    borderRadius: 12,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  referenceLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  referenceNumber: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.sm,
  },
  referenceSaved: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    textAlign: 'center',
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
    marginTop: SPACING.md,
  },
  historyButtonText: {
    color: COLORS.primaryGreen,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: SPACING.xs,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '85%',
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.xl,
    alignItems: 'center',
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  modalText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    borderRadius: 8,
    padding: SPACING.md,
    minWidth: '45%',
    alignItems: 'center',
  },
  cancelModalButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },
  confirmModalButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  cancelModalButtonText: {
    color: COLORS.darkGray,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
  },
  confirmModalButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
  },
});

export default CallScreen;
