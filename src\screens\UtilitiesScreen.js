import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

// Mock data for utility bills
const UTILITY_BILLS = [
  {
    id: '1',
    type: 'Electricity',
    accountNumber: 'EL-********',
    amount: 750.00,
    dueDate: '2023-06-15',
    status: 'unpaid',
    icon: '⚡',
  },
  {
    id: '2',
    type: 'Water',
    accountNumber: 'WT-********',
    amount: 450.00,
    dueDate: '2023-06-20',
    status: 'unpaid',
    icon: '💧',
  },
  {
    id: '3',
    type: 'Property Tax',
    accountNumber: 'PT-********',
    amount: 1200.00,
    dueDate: '2023-07-10',
    status: 'unpaid',
    icon: '🏠',
  },
  {
    id: '4',
    type: 'Waste Management',
    accountNumber: 'WM-********',
    amount: 200.00,
    dueDate: '2023-06-25',
    status: 'paid',
    icon: '🗑️',
  },
];

// Mock data for payment history
const PAYMENT_HISTORY = [
  {
    id: '1',
    type: 'Electricity',
    accountNumber: 'EL-********',
    amount: 720.00,
    date: '2023-05-12',
    reference: 'PAY-EL-123456',
    icon: '⚡',
  },
  {
    id: '2',
    type: 'Water',
    accountNumber: 'WT-********',
    amount: 430.00,
    date: '2023-05-10',
    reference: 'PAY-WT-654321',
    icon: '💧',
  },
  {
    id: '3',
    type: 'Waste Management',
    accountNumber: 'WM-********',
    amount: 200.00,
    date: '2023-05-20',
    reference: 'PAY-WM-789012',
    icon: '🗑️',
  },
];

const UtilitiesScreen = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('bills');

  // Format currency
  const formatCurrency = (amount) => {
    return `R ${amount.toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Calculate total due
  const totalDue = UTILITY_BILLS
    .filter(bill => bill.status === 'unpaid')
    .reduce((sum, bill) => sum + bill.amount, 0);

  // Render bill item
  const renderBillItem = ({ item }) => (
    <TouchableOpacity
      style={styles.billItem}
      onPress={() => navigation.navigate('PaymentDetails', { bill: item })}
    >
      <View style={[styles.billIconContainer, { backgroundColor: getBillColor(item.type) }]}>
        <Text style={styles.billIcon}>{item.icon}</Text>
      </View>
      <View style={styles.billContent}>
        <Text style={styles.billType}>{item.type}</Text>
        <Text style={styles.billAccount}>Account: {item.accountNumber}</Text>
        <Text style={styles.billDueDate}>Due: {formatDate(item.dueDate)}</Text>
      </View>
      <View style={styles.billAmountContainer}>
        <Text style={styles.billAmount}>{formatCurrency(item.amount)}</Text>
        {item.status === 'paid' ? (
          <View style={styles.paidBadge}>
            <Text style={styles.paidText}>PAID</Text>
          </View>
        ) : (
          <CustomButton
            title="Pay"
            onPress={() => navigation.navigate('PaymentDetails', { bill: item })}
            style={styles.payButton}
            textStyle={styles.payButtonText}
          />
        )}
      </View>
    </TouchableOpacity>
  );

  // Render payment history item
  const renderPaymentItem = ({ item }) => (
    <View style={styles.paymentItem}>
      <View style={[styles.paymentIconContainer, { backgroundColor: getBillColor(item.type) }]}>
        <Text style={styles.paymentIcon}>{item.icon}</Text>
      </View>
      <View style={styles.paymentContent}>
        <Text style={styles.paymentType}>{item.type}</Text>
        <Text style={styles.paymentAccount}>Account: {item.accountNumber}</Text>
        <Text style={styles.paymentDate}>Paid on: {formatDate(item.date)}</Text>
        <Text style={styles.paymentReference}>Ref: {item.reference}</Text>
      </View>
      <View style={styles.paymentAmountContainer}>
        <Text style={styles.paymentAmount}>{formatCurrency(item.amount)}</Text>
      </View>
    </View>
  );

  // Get color based on bill type
  const getBillColor = (type) => {
    switch (type) {
      case 'Electricity':
        return '#FFC107'; // Amber
      case 'Water':
        return '#2196F3'; // Blue
      case 'Property Tax':
        return '#4CAF50'; // Green
      case 'Waste Management':
        return '#795548'; // Brown
      default:
        return COLORS.primaryGreen;
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Utilities Management</Text>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Summary Card */}
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Total Amount Due</Text>
          <Text style={styles.summaryAmount}>{formatCurrency(totalDue)}</Text>
          <CustomButton
            title="Pay All Bills"
            onPress={() => {
              const firstUnpaidBill = UTILITY_BILLS.find(bill => bill.status === 'unpaid');
              if (firstUnpaidBill) {
                navigation.navigate('PaymentDetails', { bill: firstUnpaidBill });
              } else {
                alert('No unpaid bills to pay.');
              }
            }}
            style={styles.payAllButton}
          />
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'bills' && styles.activeTab]}
            onPress={() => setActiveTab('bills')}
          >
            <Text style={[styles.tabText, activeTab === 'bills' && styles.activeTabText]}>
              Current Bills
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'history' && styles.activeTab]}
            onPress={() => setActiveTab('history')}
          >
            <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
              Payment History
            </Text>
          </TouchableOpacity>
        </View>

        {/* Bills List */}
        {activeTab === 'bills' && (
          <View style={styles.billsContainer}>
            <FlatList
              data={UTILITY_BILLS}
              renderItem={renderBillItem}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              contentContainerStyle={styles.billsList}
            />
          </View>
        )}

        {/* Payment History */}
        {activeTab === 'history' && (
          <View style={styles.paymentsContainer}>
            <FlatList
              data={PAYMENT_HISTORY}
              renderItem={renderPaymentItem}
              keyExtractor={item => item.id}
              scrollEnabled={false}
              contentContainerStyle={styles.paymentsList}
            />
          </View>
        )}

        {/* Service Request Section */}
        <View style={styles.serviceRequestContainer}>
          <Text style={styles.serviceRequestTitle}>Need a New Connection?</Text>
          <Text style={styles.serviceRequestText}>
            Request new utility connections or report issues with existing services.
          </Text>
          <View style={styles.serviceButtonsContainer}>
            <CustomButton
              title="New Connection"
              onPress={() => alert('New connection request coming soon!')}
              style={[styles.serviceButton, { marginRight: SPACING.sm }]}
              type="outline"
            />
            <CustomButton
              title="Report Issue"
              onPress={() => alert('Issue reporting coming soon!')}
              style={styles.serviceButton}
              type="outline"
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  scrollView: {
    padding: SPACING.md,
    paddingBottom: 100, // Extra padding at bottom for tab bar
  },
  summaryCard: {
    backgroundColor: COLORS.primaryDarkGreen,
    borderRadius: 10,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  summaryAmount: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    marginBottom: SPACING.md,
  },
  payAllButton: {
    backgroundColor: COLORS.accentYellow,
    width: '80%',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    marginBottom: SPACING.md,
    overflow: 'hidden',
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.md,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: COLORS.primaryGreen,
  },
  tabText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
  },
  activeTabText: {
    color: COLORS.white,
  },
  billsContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  billsList: {
    paddingBottom: SPACING.xs,
  },
  billItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  billIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  billIcon: {
    fontSize: FONT_SIZES.xl,
  },
  billContent: {
    flex: 1,
  },
  billType: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  billAccount: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  billDueDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  billAmountContainer: {
    alignItems: 'flex-end',
  },
  billAmount: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  payButton: {
    height: 30,
    width: 70,
    backgroundColor: COLORS.primaryGreen,
  },
  payButtonText: {
    fontSize: FONT_SIZES.xs,
  },
  paidBadge: {
    backgroundColor: COLORS.success,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 5,
  },
  paidText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
  },
  paymentsContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  paymentsList: {
    paddingBottom: SPACING.xs,
  },
  paymentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  paymentIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  paymentIcon: {
    fontSize: FONT_SIZES.xl,
  },
  paymentContent: {
    flex: 1,
  },
  paymentType: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  paymentAccount: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  paymentDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginBottom: 2,
  },
  paymentReference: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  paymentAmountContainer: {
    alignItems: 'flex-end',
  },
  paymentAmount: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  serviceRequestContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    alignItems: 'center',
  },
  serviceRequestTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.sm,
  },
  serviceRequestText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  serviceButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  serviceButton: {
    flex: 1,
    maxWidth: 150,
  },
});

export default UtilitiesScreen;
