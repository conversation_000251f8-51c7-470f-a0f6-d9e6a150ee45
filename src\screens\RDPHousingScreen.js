import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

const RDPHousingScreen = ({ navigation }) => {
  const [hasExistingApplication, setHasExistingApplication] = useState(false);

  // Check if user has existing application (mock logic)
  useEffect(() => {
    // In a real app, this would check the user's application status from the backend
    const checkExistingApplication = () => {
      // Mock: randomly determine if user has existing application
      const hasApplication = Math.random() > 0.7; // 30% chance of having existing application
      setHasExistingApplication(hasApplication);
    };

    checkExistingApplication();
  }, []);

  const handleNewApplication = () => {
    navigation.navigate('RDPApplication');
  };

  const handleCheckStatus = () => {
    navigation.navigate('RDPStatus');
  };

  const handleRequirements = () => {
    Alert.alert(
      'RDP Housing Requirements',
      '• South African citizen or permanent resident\n' +
      '• 18 years or older\n' +
      '• Married or cohabiting, OR single with dependants\n' +
      '• Monthly household income below R3,500\n' +
      '• First-time property owner\n' +
      '• Not previously received government housing subsidy',
      [{ text: 'OK' }]
    );
  };

  const handleContactInfo = () => {
    Alert.alert(
      'Contact Information',
      'Department of Human Settlements\n\n' +
      'Call Centre: 0800 146 873\n' +
      'Email: <EMAIL>\n' +
      'Website: www.dhs.gov.za\n\n' +
      'Office Hours: Monday - Friday, 8:00 - 16:30',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>RDP Housing</Text>
        <TouchableOpacity
          style={styles.infoButton}
          onPress={handleContactInfo}
        >
          <Icon name="information" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Banner */}
        <View style={styles.bannerContainer}>
          <View style={styles.bannerContent}>
            <Text style={styles.bannerTitle}>RDP Housing Programme</Text>
            <Text style={styles.bannerSubtitle}>
              Apply for government subsidized housing for low-income families
            </Text>
          </View>
          <View style={styles.bannerIconContainer}>
            <Text style={styles.bannerIcon}>🏠</Text>
          </View>
        </View>

        {/* Status Card - Show if user has existing application */}
        {hasExistingApplication && (
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Icon name="file-document" size={24} color={COLORS.primaryGreen} />
              <Text style={styles.statusTitle}>Existing Application Found</Text>
            </View>
            <Text style={styles.statusText}>
              You have an active RDP housing application. Check your status below.
            </Text>
            <CustomButton
              title="Check Application Status"
              onPress={handleCheckStatus}
              style={styles.statusButton}
            />
          </View>
        )}

        {/* Main Actions */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Housing Services</Text>
          
          <TouchableOpacity style={styles.actionCard} onPress={handleNewApplication}>
            <View style={styles.actionIconContainer}>
              <Icon name="home-plus" size={32} color={COLORS.primaryGreen} />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Apply for RDP Housing</Text>
              <Text style={styles.actionDescription}>
                Submit a new application for government subsidized housing
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard} onPress={handleCheckStatus}>
            <View style={styles.actionIconContainer}>
              <Icon name="file-search" size={32} color={COLORS.info} />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Check Application Status</Text>
              <Text style={styles.actionDescription}>
                Track the progress of your housing application
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={COLORS.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard} onPress={handleRequirements}>
            <View style={styles.actionIconContainer}>
              <Icon name="clipboard-list" size={32} color={COLORS.warning} />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Eligibility Requirements</Text>
              <Text style={styles.actionDescription}>
                View the requirements to qualify for RDP housing
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={COLORS.gray} />
          </TouchableOpacity>
        </View>

        {/* Information Section */}
        <View style={styles.infoContainer}>
          <Text style={styles.sectionTitle}>About RDP Housing</Text>
          <View style={styles.infoCard}>
            <Text style={styles.infoText}>
              The Reconstruction and Development Programme (RDP) provides subsidized housing 
              for low-income South African families. The programme aims to provide decent, 
              affordable housing and create sustainable human settlements.
            </Text>
            
            <View style={styles.infoPoint}>
              <Icon name="check-circle" size={16} color={COLORS.success} />
              <Text style={styles.infoPointText}>Free housing for qualifying families</Text>
            </View>
            
            <View style={styles.infoPoint}>
              <Icon name="check-circle" size={16} color={COLORS.success} />
              <Text style={styles.infoPointText}>Basic services included (water, electricity)</Text>
            </View>
            
            <View style={styles.infoPoint}>
              <Icon name="check-circle" size={16} color={COLORS.success} />
              <Text style={styles.infoPointText}>Secure tenure and ownership</Text>
            </View>
          </View>
        </View>

        {/* Contact Section */}
        <View style={styles.contactContainer}>
          <Text style={styles.sectionTitle}>Need Help?</Text>
          <Text style={styles.contactText}>
            Contact the Department of Human Settlements for assistance with your application.
          </Text>
          <CustomButton
            title="Contact Department"
            onPress={handleContactInfo}
            type="outline"
            style={styles.contactButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  infoButton: {
    padding: SPACING.xs,
  },
  scrollView: {
    padding: SPACING.md,
    paddingBottom: 100,
  },
  bannerContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bannerContent: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: 4,
  },
  bannerSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray,
  },
  bannerIconContainer: {
    marginLeft: SPACING.md,
  },
  bannerIcon: {
    fontSize: 48,
  },
  statusCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.primaryGreen,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statusTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginLeft: SPACING.sm,
  },
  statusText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray,
    marginBottom: SPACING.md,
  },
  statusButton: {
    marginTop: SPACING.sm,
  },
  actionsContainer: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.md,
  },
  actionCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionIconContainer: {
    marginRight: SPACING.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray,
  },
  infoContainer: {
    marginBottom: SPACING.lg,
  },
  infoCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    lineHeight: 20,
    marginBottom: SPACING.md,
  },
  infoPoint: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  infoPointText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginLeft: SPACING.sm,
  },
  contactContainer: {
    marginBottom: SPACING.lg,
  },
  contactText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  contactButton: {
    marginTop: SPACING.sm,
  },
});

export default RDPHousingScreen;
