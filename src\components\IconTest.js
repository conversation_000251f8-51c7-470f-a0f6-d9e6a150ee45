import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS } from '../theme/colors';

const IconTest = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Icon Test</Text>
      <View style={styles.iconRow}>
        <Icon name="home" size={30} color={COLORS.primaryGreen} />
        <Icon name="account" size={30} color={COLORS.primaryGreen} />
        <Icon name="bell" size={30} color={COLORS.primaryGreen} />
        <Icon name="cog" size={30} color={COLORS.primaryGreen} />
      </View>
      <View style={styles.iconRow}>
        <Icon name="cash-multiple" size={30} color={COLORS.warning} />
        <Icon name="bell-alert" size={30} color={COLORS.warning} />
        <Icon name="account-group" size={30} color={COLORS.info} />
        <Icon name="account-circle" size={30} color={COLORS.primaryDarkGreen} />
      </View>
      <View style={styles.iconRow}>
        <Icon name="check-circle-outline" size={30} color={COLORS.success} />
        <Icon name="information-outline" size={30} color={COLORS.error} />
        <Icon name="robot" size={30} color={COLORS.primaryDarkGreen} />
        <Icon name="credit-card-outline" size={30} color={COLORS.primaryGreen} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: COLORS.white,
    borderRadius: 8,
    margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  iconRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
});

export default IconTest;
