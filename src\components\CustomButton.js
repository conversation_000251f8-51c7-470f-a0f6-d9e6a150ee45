import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { COLORS, FONT_SIZES, FONT_WEIGHTS } from '../theme/colors';

const { width } = Dimensions.get('window');

const CustomButton = ({
  title,
  onPress,
  type = 'primary',
  loading = false,
  disabled = false,
  style,
  textStyle,
  ...props
}) => {
  // Button styles based on type
  const getButtonStyle = () => {
    switch (type) {
      case 'primary':
        return styles.primaryButton;
      case 'secondary':
        return styles.secondaryButton;
      case 'outline':
        return styles.outlineButton;
      case 'text':
        return styles.textButton;
      case 'emergency':
        return styles.emergencyButton;
      default:
        return styles.primaryButton;
    }
  };

  // Text styles based on type
  const getTextStyle = () => {
    switch (type) {
      case 'primary':
        return styles.primaryText;
      case 'secondary':
        return styles.secondaryText;
      case 'outline':
        return styles.outlineText;
      case 'text':
        return styles.textButtonText;
      case 'emergency':
        return styles.emergencyText;
      default:
        return styles.primaryText;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        disabled && styles.disabledButton,
        style
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={type === 'primary' ? COLORS.white : COLORS.primaryGreen}
        />
      ) : (
        <Text style={[getTextStyle(), textStyle]}>
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    height: 50,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: width * 0.85,
    maxWidth: 400,
  },
  primaryButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  secondaryButton: {
    backgroundColor: COLORS.accentYellow,
  },
  outlineButton: {
    backgroundColor: COLORS.transparent,
    borderWidth: 2,
    borderColor: COLORS.primaryGreen,
  },
  textButton: {
    backgroundColor: COLORS.transparent,
  },
  emergencyButton: {
    backgroundColor: COLORS.error,
  },
  disabledButton: {
    opacity: 0.6,
  },
  primaryText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  secondaryText: {
    color: COLORS.primaryDarkGreen,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  outlineText: {
    color: COLORS.primaryGreen,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  textButtonText: {
    color: COLORS.primaryGreen,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
});

export default CustomButton;
