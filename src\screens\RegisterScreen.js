import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  Image,
} from 'react-native';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomInput from '../components/CustomInput';
import CustomButton from '../components/CustomButton';
import { useMockAuth } from '../context/MockAuthContext';
import { IMAGES } from '../assets/images';

const { width, height } = Dimensions.get('window');

const RegisterScreen = ({ navigation }) => {
  const [idNumber, setIdNumber] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [idNumberError, setIdNumberError] = useState('');
  const [nameError, setNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');

  const { signUp, loading, authError, clearError, needsConfirmation, tempIdNumber } = useMockAuth();

  // Check if user needs to confirm their account
  useEffect(() => {
    if (needsConfirmation && tempIdNumber) {
      navigation.navigate('Confirmation', { idNumber: tempIdNumber });
    }
  }, [needsConfirmation, tempIdNumber, navigation]);

  const validateIdNumber = (idNumber) => {
    // South African ID number is 13 digits
    const idNumberRegex = /^\d{13}$/;
    if (!idNumber) {
      setIdNumberError('ID Number is required');
      return false;
    } else if (!idNumberRegex.test(idNumber)) {
      setIdNumberError('Please enter a valid 13-digit ID Number');
      return false;
    }
    setIdNumberError('');
    return true;
  };

  const validateName = (name) => {
    if (!name) {
      setNameError('Name is required');
      return false;
    } else if (name.length < 2) {
      setNameError('Name must be at least 2 characters');
      return false;
    }
    setNameError('');
    return true;
  };

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email');
      return false;
    }
    setEmailError('');
    return true;
  };

  const validatePassword = (password) => {
    if (!password) {
      setPasswordError('Password is required');
      return false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      return false;
    }
    setPasswordError('');
    return true;
  };

  const validateConfirmPassword = (confirmPassword) => {
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your password');
      return false;
    } else if (confirmPassword !== password) {
      setConfirmPasswordError('Passwords do not match');
      return false;
    }
    setConfirmPasswordError('');
    return true;
  };

  const handleRegister = async () => {
    const isIdNumberValid = validateIdNumber(idNumber);
    const isNameValid = validateName(name);
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);
    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);

    if (isIdNumberValid && isNameValid && isEmailValid && isPasswordValid && isConfirmPasswordValid) {
      const result = await signUp(idNumber, password, name, email);

      if (result.success) {
        // The user will be redirected to the confirmation screen via the useEffect hook
        console.log('Registration successful, redirecting to confirmation screen');
      } else {
        console.error('Registration failed:', result.error);
      }
    }
  };

  const handleLogin = () => {
    // Navigate to login screen
    navigation.navigate('Login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.appName}>Let's Talk</Text>
            <Text style={styles.tagline}>Join our community today</Text>
          </View>

          <View style={styles.logoContainer}>
            <Image
              source={IMAGES.logo}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.welcomeText}>Create Account</Text>
            <Text style={styles.registerText}>Sign up to get started</Text>

            {authError && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{authError}</Text>
                <TouchableOpacity onPress={clearError}>
                  <Text style={styles.dismissText}>Dismiss</Text>
                </TouchableOpacity>
              </View>
            )}

            <CustomInput
              label="ID Number"
              placeholder="Enter your 13-digit ID Number"
              value={idNumber}
              onChangeText={(text) => {
                // Only allow digits and limit to 13 characters
                const sanitizedValue = text.replace(/[^0-9]/g, '').slice(0, 13);
                setIdNumber(sanitizedValue);
                if (idNumberError) validateIdNumber(sanitizedValue);
              }}
              keyboardType="numeric"
              error={idNumberError}
              maxLength={13}
            />

            <CustomInput
              label="Full Name"
              placeholder="Enter your full name"
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (nameError) validateName(text);
              }}
              error={nameError}
              autoCapitalize="words"
            />

            <CustomInput
              label="Email"
              placeholder="Enter your email"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                if (emailError) validateEmail(text);
              }}
              keyboardType="email-address"
              error={emailError}
              autoCapitalize="none"
            />

            <CustomInput
              label="Password"
              placeholder="Create a password"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                if (passwordError) validatePassword(text);
                if (confirmPassword && confirmPasswordError) {
                  validateConfirmPassword(confirmPassword);
                }
              }}
              secureTextEntry
              error={passwordError}
            />

            <CustomInput
              label="Confirm Password"
              placeholder="Confirm your password"
              value={confirmPassword}
              onChangeText={(text) => {
                setConfirmPassword(text);
                if (confirmPasswordError) validateConfirmPassword(text);
              }}
              secureTextEntry
              error={confirmPasswordError}
            />

            <CustomButton
              title="Register"
              onPress={handleRegister}
              loading={loading}
              style={styles.registerButton}
            />

            <View style={styles.loginContainer}>
              <Text style={styles.haveAccountText}>Already have an account? </Text>
              <TouchableOpacity onPress={handleLogin}>
                <Text style={styles.loginText}>Login</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: SPACING.xl,
  },
  header: {
    alignItems: 'center',
    marginTop: SPACING.xl,
    marginBottom: SPACING.xl,
  },
  appName: {
    fontSize: FONT_SIZES.title,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.xs,
  },
  tagline: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  logoContainer: {
    marginVertical: SPACING.md,
    alignItems: 'center',
  },
  logo: {
    width: 150,
    height: 150,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  formContainer: {
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  welcomeText: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  registerText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginBottom: SPACING.xl,
    textAlign: 'center',
  },
  errorContainer: {
    width: '100%',
    backgroundColor: '#FFEBEE',
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: FONT_SIZES.sm,
    flex: 1,
  },
  dismissText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
    fontSize: FONT_SIZES.sm,
  },
  registerButton: {
    marginTop: SPACING.lg,
  },
  loginContainer: {
    flexDirection: 'row',
    marginTop: SPACING.xl,
    alignItems: 'center',
  },
  haveAccountText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  loginText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
  },
});

export default RegisterScreen;
