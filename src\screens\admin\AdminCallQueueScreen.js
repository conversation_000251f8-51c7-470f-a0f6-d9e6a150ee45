import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import { useMockAuth } from '../../context/MockAuthContext';
import mockCallData from '../../data/mockCallData';

const AdminCallQueueScreen = ({ navigation }) => {
  const { currentUser } = useMockAuth();
  const [callQueue, setCallQueue] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCall, setSelectedCall] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  // Load call queue on mount
  useEffect(() => {
    loadCallQueue();
  }, []);

  // Load call queue
  const loadCallQueue = async () => {
    try {
      setRefreshing(true);
      const queue = await mockCallData.getCallQueue();
      setCallQueue(queue);
      setRefreshing(false);
    } catch (error) {
      console.error('Error loading call queue:', error);
      setRefreshing(false);
      Alert.alert('Error', 'Failed to load call queue');
    }
  };

  // Handle refresh
  const onRefresh = () => {
    loadCallQueue();
  };

  // Handle call selection
  const handleCallSelect = (call) => {
    setSelectedCall(call);
    setModalVisible(true);
  };

  // Handle accept call
  const handleAcceptCall = async () => {
    if (!selectedCall) return;
    
    try {
      // Update call status
      await mockCallData.updateCallInQueue(selectedCall.id, { status: 'in-progress' });
      
      // Close modal
      setModalVisible(false);
      
      // Navigate to call details screen
      navigation.navigate('AdminCallDetails', { callId: selectedCall.id });
      
      // Refresh call queue
      loadCallQueue();
    } catch (error) {
      console.error('Error accepting call:', error);
      Alert.alert('Error', 'Failed to accept call');
    }
  };

  // Handle reject call
  const handleRejectCall = async (reason) => {
    if (!selectedCall) return;
    
    try {
      // Update call status
      await mockCallData.updateCallInQueue(selectedCall.id, { 
        status: 'rejected',
        rejectionReason: reason,
        rejectedBy: currentUser.name,
        rejectedAt: new Date().toISOString()
      });
      
      // Remove call from queue
      await mockCallData.removeCallFromQueue(selectedCall.id);
      
      // Close modal
      setModalVisible(false);
      
      // Refresh call queue
      loadCallQueue();
      
      Alert.alert('Success', 'Call has been rejected');
    } catch (error) {
      console.error('Error rejecting call:', error);
      Alert.alert('Error', 'Failed to reject call');
    }
  };

  // Format time elapsed
  const formatTimeElapsed = (timestamp) => {
    const callTime = new Date(timestamp).getTime();
    const now = new Date().getTime();
    const elapsed = now - callTime;
    
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  // Render call item
  const renderCallItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.callItem}
      onPress={() => handleCallSelect(item)}
    >
      <View style={styles.callHeader}>
        <View style={styles.callerInfo}>
          <Text style={styles.callerName}>{item.userName}</Text>
          <Text style={styles.callerId}>ID: {item.userIdNumber || 'Anonymous'}</Text>
        </View>
        <View style={styles.queueInfo}>
          <Text style={styles.queuePosition}>#{item.queuePosition}</Text>
          <View style={styles.waitingIndicator}>
            <Text style={styles.waitingText}>Waiting</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.callDetails}>
        <View style={styles.detailItem}>
          <Icon name="phone" size={16} color={COLORS.darkGray} />
          <Text style={styles.detailText}>{item.phoneNumber || 'No phone number'}</Text>
        </View>
        <View style={styles.detailItem}>
          <Icon name="clock-outline" size={16} color={COLORS.darkGray} />
          <Text style={styles.detailText}>
            Waiting for {formatTimeElapsed(item.timestamp)}
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Icon name="timer-sand" size={16} color={COLORS.darkGray} />
          <Text style={styles.detailText}>
            Est. wait: {item.estimatedWaitTime}
          </Text>
        </View>
      </View>
      
      <View style={styles.callActions}>
        <TouchableOpacity 
          style={[styles.actionButton, styles.acceptButton]}
          onPress={() => handleCallSelect(item)}
        >
          <Icon name="phone-in-talk" size={20} color={COLORS.white} />
          <Text style={styles.actionButtonText}>Accept</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.actionButton, styles.rejectButton]}
          onPress={() => {
            setSelectedCall(item);
            Alert.alert(
              'Reject Call',
              'Are you sure you want to reject this call?',
              [
                { text: 'Cancel', style: 'cancel' },
                { 
                  text: 'Busy', 
                  onPress: () => handleRejectCall('Agent busy with other calls')
                },
                { 
                  text: 'Reject', 
                  style: 'destructive',
                  onPress: () => handleRejectCall('Call rejected by agent')
                },
              ]
            );
          }}
        >
          <Icon name="phone-off" size={20} color={COLORS.white} />
          <Text style={styles.actionButtonText}>Reject</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Call Queue</Text>
        <TouchableOpacity 
          style={styles.refreshButton}
          onPress={onRefresh}
        >
          <Icon name="refresh" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Call Queue */}
      <FlatList
        data={callQueue}
        renderItem={renderCallItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="phone-off" size={60} color={COLORS.lightGray} />
            <Text style={styles.emptyText}>No calls in queue</Text>
            <Text style={styles.emptySubtext}>
              All calls have been handled or there are no incoming calls
            </Text>
          </View>
        }
      />

      {/* Call Action Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Incoming Call</Text>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Icon name="close" size={24} color={COLORS.darkGray} />
              </TouchableOpacity>
            </View>
            
            {selectedCall && (
              <View style={styles.modalBody}>
                <View style={styles.callerDetails}>
                  <Icon name="account-circle" size={60} color={COLORS.primaryGreen} />
                  <Text style={styles.modalCallerName}>{selectedCall.userName}</Text>
                  <Text style={styles.modalCallerId}>
                    ID: {selectedCall.userIdNumber || 'Anonymous'}
                  </Text>
                  <Text style={styles.modalCallerPhone}>
                    {selectedCall.phoneNumber || 'No phone number'}
                  </Text>
                </View>
                
                <View style={styles.callStatusInfo}>
                  <View style={styles.statusItem}>
                    <Icon name="clock-outline" size={20} color={COLORS.darkGray} />
                    <Text style={styles.statusText}>
                      Waiting for {formatTimeElapsed(selectedCall.timestamp)}
                    </Text>
                  </View>
                  <View style={styles.statusItem}>
                    <Icon name="account-multiple" size={20} color={COLORS.darkGray} />
                    <Text style={styles.statusText}>
                      Queue position: #{selectedCall.queuePosition}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.modalActions}>
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.rejectModalButton]}
                    onPress={() => {
                      Alert.alert(
                        'Reject Call',
                        'Are you sure you want to reject this call?',
                        [
                          { text: 'Cancel', style: 'cancel' },
                          { 
                            text: 'Busy', 
                            onPress: () => handleRejectCall('Agent busy with other calls')
                          },
                          { 
                            text: 'Reject', 
                            style: 'destructive',
                            onPress: () => handleRejectCall('Call rejected by agent')
                          },
                        ]
                      );
                    }}
                  >
                    <Icon name="phone-off" size={24} color={COLORS.white} />
                    <Text style={styles.modalButtonText}>Reject</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={[styles.modalButton, styles.acceptModalButton]}
                    onPress={handleAcceptCall}
                  >
                    <Icon name="phone-in-talk" size={24} color={COLORS.white} />
                    <Text style={styles.modalButtonText}>Accept</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  refreshButton: {
    padding: SPACING.xs,
  },
  listContainer: {
    padding: SPACING.md,
    paddingBottom: SPACING.xxl,
  },
  callItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  callHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  callerInfo: {
    flex: 1,
  },
  callerName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  callerId: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  queueInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  queuePosition: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginRight: SPACING.sm,
  },
  waitingIndicator: {
    backgroundColor: '#FFF8E1',
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: 4,
  },
  waitingText: {
    fontSize: FONT_SIZES.xs,
    color: '#FFA000',
    fontWeight: FONT_WEIGHTS.medium,
  },
  callDetails: {
    marginBottom: SPACING.sm,
    paddingBottom: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  detailText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: SPACING.xs,
  },
  callActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 5,
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  acceptButton: {
    backgroundColor: COLORS.success,
  },
  rejectButton: {
    backgroundColor: COLORS.error,
  },
  actionButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: SPACING.xs,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginTop: SPACING.sm,
    paddingHorizontal: SPACING.xl,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.primaryGreen,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  modalTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  modalBody: {
    padding: SPACING.lg,
  },
  callerDetails: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  modalCallerName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginTop: SPACING.sm,
  },
  modalCallerId: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  modalCallerPhone: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
    marginTop: SPACING.xs,
  },
  callStatusInfo: {
    marginBottom: SPACING.lg,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statusText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginLeft: SPACING.sm,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 5,
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  acceptModalButton: {
    backgroundColor: COLORS.success,
  },
  rejectModalButton: {
    backgroundColor: COLORS.error,
  },
  modalButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: SPACING.sm,
  },
});

export default AdminCallQueueScreen;
