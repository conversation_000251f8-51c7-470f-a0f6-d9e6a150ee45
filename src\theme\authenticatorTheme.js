import { COLORS, FONT_SIZES, FONT_WEIGHTS } from './colors';

// Custom theme for the Authenticator component
export const authenticatorTheme = {
  tokens: {
    colors: {
      brand: {
        primary: {
          10: COLORS.lightGreen,
          20: COLORS.primaryGreen,
          40: COLORS.primaryGreen,
          60: COLORS.primaryGreen,
          80: COLORS.darkGreen,
          90: COLORS.darkGreen,
          100: COLORS.darkGreen,
        },
        secondary: {
          10: COLORS.lightYellow,
          20: COLORS.yellow,
          40: COLORS.yellow,
          60: COLORS.yellow,
          80: COLORS.darkYellow,
          90: COLORS.darkYellow,
          100: COLORS.darkYellow,
        },
      },
      background: {
        primary: COLORS.white,
        secondary: COLORS.lightGray,
      },
      font: {
        primary: COLORS.black,
        secondary: COLORS.darkGray,
        tertiary: COLORS.gray,
      },
      border: {
        primary: COLORS.lightGray,
        secondary: COLORS.gray,
        tertiary: COLORS.darkGray,
      },
    },
    fontSizes: {
      xs: FONT_SIZES.xs,
      sm: FONT_SIZES.sm,
      md: FONT_SIZES.md,
      lg: FONT_SIZES.lg,
      xl: FONT_SIZES.xl,
      xxl: FONT_SIZES.xxl,
    },
    fontWeights: {
      light: FONT_WEIGHTS.light,
      normal: FONT_WEIGHTS.normal,
      semibold: FONT_WEIGHTS.semibold,
      bold: FONT_WEIGHTS.bold,
    },
    space: {
      xs: 4,
      sm: 8,
      md: 12,
      lg: 16,
      xl: 24,
      xxl: 32,
    },
    radii: {
      xs: 4,
      sm: 8,
      md: 12,
      lg: 16,
      xl: 24,
    },
  },
  components: {
    button: {
      primary: {
        backgroundColor: COLORS.primaryGreen,
        color: COLORS.white,
        borderRadius: 8,
        fontWeight: FONT_WEIGHTS.semibold,
      },
      secondary: {
        backgroundColor: 'transparent',
        color: COLORS.primaryGreen,
        borderColor: COLORS.primaryGreen,
        borderWidth: 1,
        borderRadius: 8,
        fontWeight: FONT_WEIGHTS.semibold,
      },
    },
    input: {
      borderColor: COLORS.lightGray,
      borderRadius: 8,
      color: COLORS.black,
      fontSize: FONT_SIZES.md,
    },
    text: {
      color: COLORS.black,
      fontSize: FONT_SIZES.md,
    },
    heading: {
      color: COLORS.black,
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
    },
    link: {
      color: COLORS.primaryGreen,
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
    },
  },
};

export default authenticatorTheme;
