import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';

// Mock AI responses
const AI_RESPONSES = {
  greeting: "Hello! I'm <PERSON><PERSON>, your virtual assistant. How can I help you today?",
  services: "I can help you with information about government services, utility bills, community events, and more. What would you like to know about?",
  idApplication: "To apply for a new ID, you'll need to visit your nearest Home Affairs office with your birth certificate and proof of residence. The process typically takes 2-3 weeks.",
  waterBill: "Your current water bill is R450.00, due on June 20th. Would you like to make a payment now?",
  electricityOutage: "I see there's a scheduled electricity maintenance in your area on June 15th from 9:00 AM to 2:00 PM. Would you like to receive a reminder notification?",
  communityMeeting: "The next community meeting is scheduled for June 8th at 6:00 PM at the town hall. The main topics will be road maintenance and the new recycling program.",
  reportIssue: "I can help you report an issue. What type of issue would you like to report? (Options: Water, Electricity, Roads, Waste, Other)",
  roadIssue: "I've recorded your road maintenance issue. Your reference number is RM-2023-1234. A municipal representative will assess the situation within 48 hours.",
  default: "I'm not sure I understand. Could you please rephrase your question or select from one of these topics: Government Services, Utility Bills, Community Events, or Report an Issue?",
};

// Suggested questions
const SUGGESTED_QUESTIONS = [
  "How do I apply for a new ID?",
  "What's my current water bill?",
  "Are there any electricity outages planned?",
  "When is the next community meeting?",
  "I want to report a pothole on my street",
];

const ThusongAIScreen = () => {
  // We'll use the auth context in future implementations
  // const { currentUser } = useMockAuth();
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const scrollViewRef = useRef();

  // Initialize chat with greeting
  useEffect(() => {
    setTimeout(() => {
      setMessages([
        {
          id: '1',
          text: AI_RESPONSES.greeting,
          sender: 'ai',
          timestamp: new Date(),
        },
        {
          id: '2',
          text: AI_RESPONSES.services,
          sender: 'ai',
          timestamp: new Date(),
        },
      ]);
    }, 500);
  }, []);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Send message
  const sendMessage = () => {
    if (inputText.trim() === '') return;

    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate AI thinking
    setTimeout(() => {
      // Generate AI response based on user input
      const aiResponse = generateAIResponse(inputText);

      // Add AI response
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        sender: 'ai',
        timestamp: new Date(),
      };

      setMessages(prevMessages => [...prevMessages, aiMessage]);
      setIsTyping(false);
    }, 1000);
  };

  // Generate AI response based on user input
  const generateAIResponse = (input) => {
    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('id') || lowerInput.includes('apply')) {
      return AI_RESPONSES.idApplication;
    } else if (lowerInput.includes('water') && lowerInput.includes('bill')) {
      return AI_RESPONSES.waterBill;
    } else if (lowerInput.includes('electricity') || lowerInput.includes('outage') || lowerInput.includes('power')) {
      return AI_RESPONSES.electricityOutage;
    } else if (lowerInput.includes('community') || lowerInput.includes('meeting')) {
      return AI_RESPONSES.communityMeeting;
    } else if (lowerInput.includes('report') || lowerInput.includes('issue') || lowerInput.includes('problem')) {
      return AI_RESPONSES.reportIssue;
    } else if (lowerInput.includes('pothole') || lowerInput.includes('road')) {
      return AI_RESPONSES.roadIssue;
    } else {
      return AI_RESPONSES.default;
    }
  };

  // Handle suggested question
  const handleSuggestedQuestion = (question) => {
    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      text: question,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    setIsTyping(true);

    // Simulate AI thinking
    setTimeout(() => {
      // Generate AI response based on suggested question
      const aiResponse = generateAIResponse(question);

      // Add AI response
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        sender: 'ai',
        timestamp: new Date(),
      };

      setMessages(prevMessages => [...prevMessages, aiMessage]);
      setIsTyping(false);
    }, 1000);
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const hours = timestamp.getHours();
    const minutes = timestamp.getMinutes();
    return `${hours}:${minutes < 10 ? '0' + minutes : minutes}`;
  };

  // Render message item
  const renderMessageItem = ({ item }) => (
    <View style={[
      styles.messageContainer,
      item.sender === 'user' ? styles.userMessageContainer : styles.aiMessageContainer
    ]}>
      <View style={[
        styles.messageBubble,
        item.sender === 'user' ? styles.userMessageBubble : styles.aiMessageBubble
      ]}>
        <Text style={[
          styles.messageText,
          item.sender === 'user' ? styles.userMessageText : styles.aiMessageText
        ]}>
          {item.text}
        </Text>
      </View>
      <Text style={styles.messageTimestamp}>
        {formatTimestamp(new Date(item.timestamp))}
      </Text>
    </View>
  );

  // Render suggested question
  const renderSuggestedQuestion = ({ item }) => (
    <TouchableOpacity
      style={styles.suggestedQuestionButton}
      onPress={() => handleSuggestedQuestion(item)}
    >
      <Text style={styles.suggestedQuestionText}>{item}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Thusong AI Assistant</Text>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        {/* Chat Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
        >
          {messages.map(message => (
            <React.Fragment key={message.id}>
              {renderMessageItem({ item: message })}
            </React.Fragment>
          ))}

          {isTyping && (
            <View style={[styles.messageContainer, styles.aiMessageContainer]}>
              <View style={[styles.messageBubble, styles.aiMessageBubble]}>
                <Text style={styles.typingIndicator}>Thusong AI is typing...</Text>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Suggested Questions */}
        {messages.length <= 3 && (
          <View style={styles.suggestedQuestionsContainer}>
            <Text style={styles.suggestedQuestionsTitle}>Suggested Questions:</Text>
            <FlatList
              data={SUGGESTED_QUESTIONS}
              renderItem={renderSuggestedQuestion}
              keyExtractor={(_, index) => index.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.suggestedQuestionsList}
            />
          </View>
        )}

        {/* Input Area */}
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Type your message..."
            value={inputText}
            onChangeText={setInputText}
            multiline
          />
          <TouchableOpacity
            style={[styles.sendButton, !inputText.trim() && styles.disabledSendButton]}
            onPress={sendMessage}
            disabled={!inputText.trim()}
          >
            <Text style={styles.sendButtonText}>Send</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.md,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    padding: SPACING.md,
  },
  messagesContent: {
    paddingBottom: 100, // Extra padding at bottom for tab bar
  },
  messageContainer: {
    marginBottom: SPACING.md,
    maxWidth: '80%',
  },
  userMessageContainer: {
    alignSelf: 'flex-end',
  },
  aiMessageContainer: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: 20,
    padding: SPACING.md,
  },
  userMessageBubble: {
    backgroundColor: COLORS.primaryGreen,
    borderBottomRightRadius: 5,
  },
  aiMessageBubble: {
    backgroundColor: COLORS.white,
    borderBottomLeftRadius: 5,
  },
  messageText: {
    fontSize: FONT_SIZES.md,
  },
  userMessageText: {
    color: COLORS.white,
  },
  aiMessageText: {
    color: COLORS.black,
  },
  messageTimestamp: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginTop: 2,
    alignSelf: 'flex-end',
  },
  typingIndicator: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  suggestedQuestionsContainer: {
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
  },
  suggestedQuestionsTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.darkGray,
    marginBottom: SPACING.sm,
  },
  suggestedQuestionsList: {
    paddingBottom: SPACING.xs,
  },
  suggestedQuestionButton: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 20,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginRight: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },
  suggestedQuestionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    alignItems: 'center',
  },
  input: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
    borderRadius: 20,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    maxHeight: 100,
    fontSize: FONT_SIZES.md,
  },
  sendButton: {
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 20,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginLeft: SPACING.sm,
  },
  disabledSendButton: {
    backgroundColor: COLORS.lightGray,
  },
  sendButtonText: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.bold,
  },
});

export default ThusongAIScreen;
