import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../../theme/colors';
import {
  PATIENT_VISITS_DATA,
  PRESCRIPTION_TRENDS_DATA,
  APPOINTMENT_STATUS_DATA,
  COMMON_DIAGNOSES_DATA,
  PATIENT_AGE_DISTRIBUTION,
  MONTHLY_REVENUE_DATA,
  SUMMARY_STATS,
  RECENT_ACTIVITIES,
  PERFORMANCE_METRICS,
  CHART_CONFIG,
} from '../../data/mockReportsData';

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 32;

const DoctorReportsScreen = ({ navigation }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  // Custom Bar Chart Component
  const CustomBarChart = ({ data, title }) => {
    const maxValue = Math.max(...data.datasets[0].data);

    return (
      <View style={styles.customChart}>
        <Text style={styles.chartTitle}>{title}</Text>
        <View style={styles.barChartContainer}>
          {data.labels.map((label, index) => {
            const value = data.datasets[0].data[index];
            const height = (value / maxValue) * 120;

            return (
              <View key={index} style={styles.barItem}>
                <View style={styles.barContainer}>
                  <View
                    style={[
                      styles.bar,
                      {
                        height: height,
                        backgroundColor: COLORS.primaryGreen
                      }
                    ]}
                  />
                </View>
                <Text style={styles.barValue}>{value}</Text>
                <Text style={styles.barLabel}>{label}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  // Custom Line Chart Component
  const CustomLineChart = ({ data, title }) => {
    const maxValue = Math.max(...data.datasets[0].data);
    const minValue = Math.min(...data.datasets[0].data);
    const range = maxValue - minValue;

    return (
      <View style={styles.customChart}>
        <Text style={styles.chartTitle}>{title}</Text>
        <View style={styles.lineChartContainer}>
          <View style={styles.lineChart}>
            {data.datasets[0].data.map((value, index) => {
              const height = range > 0 ? ((value - minValue) / range) * 100 : 50;
              const left = (index / (data.datasets[0].data.length - 1)) * 100;

              return (
                <View
                  key={index}
                  style={[
                    styles.linePoint,
                    {
                      left: `${left}%`,
                      bottom: `${height}%`,
                    }
                  ]}
                >
                  <View style={styles.point} />
                  <Text style={styles.pointValue}>{value}</Text>
                </View>
              );
            })}
          </View>
          <View style={styles.lineLabels}>
            {data.labels.map((label, index) => (
              <Text key={index} style={styles.lineLabel}>{label}</Text>
            ))}
          </View>
        </View>
      </View>
    );
  };

  // Custom Pie Chart Component
  const CustomPieChart = ({ data, title }) => {
    const total = data.reduce((sum, item) => sum + item.population, 0);

    return (
      <View style={styles.customChart}>
        <Text style={styles.chartTitle}>{title}</Text>
        <View style={styles.pieChartContainer}>
          {data.map((item, index) => {
            const percentage = ((item.population / total) * 100).toFixed(1);

            return (
              <View key={index} style={styles.pieItem}>
                <View
                  style={[
                    styles.pieColorBox,
                    { backgroundColor: item.color }
                  ]}
                />
                <View style={styles.pieTextContainer}>
                  <Text style={styles.pieName}>{item.name}</Text>
                  <Text style={styles.piePercentage}>{percentage}%</Text>
                </View>
                <Text style={styles.pieValue}>{item.population}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  // Render header with back button
  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Icon name="arrow-left" size={24} color={COLORS.primaryGreen} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Reports & Analytics</Text>
      <TouchableOpacity
        style={styles.exportButton}
        onPress={() => Alert.alert('Export', 'Export functionality coming soon!')}
      >
        <Icon name="download" size={24} color={COLORS.primaryGreen} />
      </TouchableOpacity>
    </View>
  );

  // Render period selector
  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {['week', 'month', 'quarter'].map((period) => (
        <TouchableOpacity
          key={period}
          style={[
            styles.periodButton,
            selectedPeriod === period && styles.selectedPeriodButton
          ]}
          onPress={() => setSelectedPeriod(period)}
        >
          <Text
            style={[
              styles.periodButtonText,
              selectedPeriod === period && styles.selectedPeriodButtonText
            ]}
          >
            {period.charAt(0).toUpperCase() + period.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  // Render summary stats cards
  const renderSummaryStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statsRow}>
        <View style={styles.statCard}>
          <Icon name="account-group" size={24} color={COLORS.primaryGreen} />
          <Text style={styles.statValue}>{SUMMARY_STATS.totalPatients}</Text>
          <Text style={styles.statLabel}>Total Patients</Text>
        </View>
        <View style={styles.statCard}>
          <Icon name="calendar-check" size={24} color={COLORS.info} />
          <Text style={styles.statValue}>{SUMMARY_STATS.totalAppointments}</Text>
          <Text style={styles.statLabel}>Appointments</Text>
        </View>
      </View>
      <View style={styles.statsRow}>
        <View style={styles.statCard}>
          <Icon name="pill" size={24} color={COLORS.warning} />
          <Text style={styles.statValue}>{SUMMARY_STATS.totalPrescriptions}</Text>
          <Text style={styles.statLabel}>Prescriptions</Text>
        </View>
        <View style={styles.statCard}>
          <Icon name="star" size={24} color={COLORS.success} />
          <Text style={styles.statValue}>{SUMMARY_STATS.patientSatisfaction}</Text>
          <Text style={styles.statLabel}>Satisfaction</Text>
        </View>
      </View>
    </View>
  );



  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {renderHeader()}

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderPeriodSelector()}

        {renderSummaryStats()}

        {/* Patient Visits Chart */}
        <CustomLineChart
          data={PATIENT_VISITS_DATA}
          title="Patient Visits (Last 7 Days)"
        />

        {/* Appointment Status Distribution */}
        <CustomPieChart
          data={APPOINTMENT_STATUS_DATA}
          title="Appointment Status Distribution"
        />

        {/* Common Diagnoses */}
        <CustomBarChart
          data={COMMON_DIAGNOSES_DATA}
          title="Most Common Diagnoses"
        />

        {/* Patient Age Distribution */}
        <CustomPieChart
          data={PATIENT_AGE_DISTRIBUTION}
          title="Patient Age Distribution"
        />

        {/* Performance Metrics */}
        <View style={styles.metricsSection}>
          <Text style={styles.chartTitle}>Performance Metrics</Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>{PERFORMANCE_METRICS.appointmentsPerDay}</Text>
              <Text style={styles.metricLabel}>Avg Appointments/Day</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>{PERFORMANCE_METRICS.averageConsultationTime}m</Text>
              <Text style={styles.metricLabel}>Avg Consultation</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>{PERFORMANCE_METRICS.prescriptionAccuracy}%</Text>
              <Text style={styles.metricLabel}>Prescription Accuracy</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>{PERFORMANCE_METRICS.onTimePerformance}%</Text>
              <Text style={styles.metricLabel}>On-Time Performance</Text>
            </View>
          </View>
        </View>

        {/* Recent Activities */}
        <View style={styles.activitiesSection}>
          <Text style={styles.chartTitle}>Recent Activities</Text>
          {RECENT_ACTIVITIES.map((activity) => (
            <View key={activity.id} style={styles.activityItem}>
              <View style={[styles.activityIcon, { backgroundColor: activity.color }]}>
                <Icon name={activity.icon} size={16} color={COLORS.white} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityDescription}>{activity.description}</Text>
                <Text style={styles.activityTime}>{activity.time}</Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  exportButton: {
    padding: SPACING.xs,
  },
  scrollView: {
    flex: 1,
  },
  periodSelector: {
    flexDirection: 'row',
    margin: SPACING.md,
    backgroundColor: COLORS.lightGray,
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: SPACING.sm,
    alignItems: 'center',
    borderRadius: 6,
  },
  selectedPeriodButton: {
    backgroundColor: COLORS.primaryGreen,
  },
  periodButtonText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
  },
  selectedPeriodButtonText: {
    color: COLORS.white,
  },
  statsContainer: {
    margin: SPACING.md,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: SPACING.sm,
  },
  statCard: {
    flex: 1,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: SPACING.xs,
    elevation: 2,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statValue: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginTop: SPACING.xs,
  },
  statLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
    textAlign: 'center',
    marginTop: 4,
  },
  chartTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.md,
  },
  metricsSection: {
    margin: SPACING.md,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    elevation: 2,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    width: '48%',
    backgroundColor: COLORS.background,
    padding: SPACING.md,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  metricValue: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  metricLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
    textAlign: 'center',
    marginTop: 4,
  },
  activitiesSection: {
    margin: SPACING.md,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    elevation: 2,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.sm,
  },
  activityContent: {
    flex: 1,
  },
  activityDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
  },
  activityTime: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
    marginTop: 2,
  },
  bottomPadding: {
    height: SPACING.xl,
  },
  // Custom Chart Styles
  customChart: {
    margin: SPACING.md,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    elevation: 2,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  barChartContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    height: 150,
    paddingTop: 20,
  },
  barItem: {
    alignItems: 'center',
    flex: 1,
  },
  barContainer: {
    height: 120,
    justifyContent: 'flex-end',
    alignItems: 'center',
    width: 30,
  },
  bar: {
    width: 25,
    borderRadius: 4,
    minHeight: 5,
  },
  barValue: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginTop: 4,
  },
  barLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
    marginTop: 2,
    textAlign: 'center',
  },
  lineChartContainer: {
    height: 150,
  },
  lineChart: {
    height: 120,
    position: 'relative',
    backgroundColor: COLORS.background,
    borderRadius: 8,
    marginBottom: 10,
  },
  linePoint: {
    position: 'absolute',
    alignItems: 'center',
  },
  point: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.primaryGreen,
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  pointValue: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
    marginTop: 2,
    backgroundColor: COLORS.white,
    paddingHorizontal: 4,
    borderRadius: 2,
  },
  lineLabels: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  lineLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
  },
  pieChartContainer: {
    paddingVertical: 10,
  },
  pieItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  pieColorBox: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  pieTextContainer: {
    flex: 1,
  },
  pieName: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.medium,
  },
  piePercentage: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primaryGreen,
  },
  pieValue: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
  },
});

export default DoctorReportsScreen;
