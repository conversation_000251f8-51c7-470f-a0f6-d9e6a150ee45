import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';

// Mock weather data (in a real app, this would come from an API)
const MOCK_WEATHER = {
  location: 'Johannesburg',
  temperature: 24,
  condition: 'Sunny',
  icon: 'weather-sunny',
  high: 28,
  low: 18,
  humidity: 45,
  wind: 12,
};

const WeatherWidget = ({ style }) => {
  const [weather, setWeather] = useState(MOCK_WEATHER);
  const [expanded, setExpanded] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  if (loading) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.loadingContainer}>
          <Icon name="weather-partly-cloudy" size={24} color={COLORS.lightGray} />
          <Text style={styles.loadingText}>Loading weather...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={styles.mainContent}
        onPress={toggleExpand}
        activeOpacity={0.8}
      >
        <View style={styles.locationContainer}>
          <Icon name="map-marker" size={16} color={COLORS.primaryGreen} />
          <Text style={styles.locationText}>{weather.location}</Text>
        </View>

        <View style={styles.currentWeather}>
          <Icon name={weather.icon} size={36} color={COLORS.primaryGreen} />
          <Text style={styles.temperatureText}>{weather.temperature}°C</Text>
          <Text style={styles.conditionText}>{weather.condition}</Text>
        </View>

        <View style={styles.highLowContainer}>
          <View style={styles.highLowItem}>
            <Icon name="arrow-up" size={14} color={COLORS.error} />
            <Text style={styles.highLowText}>{weather.high}°</Text>
          </View>
          <View style={styles.highLowItem}>
            <Icon name="arrow-down" size={14} color={COLORS.info} />
            <Text style={styles.highLowText}>{weather.low}°</Text>
          </View>
        </View>

        <Icon
          name={expanded ? "chevron-up" : "chevron-down"}
          size={20}
          color={COLORS.darkGray}
          style={styles.expandIcon}
        />
      </TouchableOpacity>

      {expanded && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailsContent}>
            <View style={styles.detailItem}>
              <Icon name="water-percent" size={18} color={COLORS.info} />
              <Text style={styles.detailLabel}>Humidity</Text>
              <Text style={styles.detailValue}>{weather.humidity}%</Text>
            </View>

            <View style={styles.detailItem}>
              <Icon name="weather-windy" size={18} color={COLORS.info} />
              <Text style={styles.detailLabel}>Wind</Text>
              <Text style={styles.detailValue}>{weather.wind} km/h</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    overflow: 'hidden',
  },
  loadingContainer: {
    padding: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  loadingText: {
    marginLeft: SPACING.sm,
    color: COLORS.darkGray,
    fontSize: FONT_SIZES.sm,
  },
  mainContent: {
    padding: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: 4,
  },
  currentWeather: {
    alignItems: 'center',
    flex: 1,
  },
  temperatureText: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  conditionText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  highLowContainer: {
    flexDirection: 'row',
  },
  highLowItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: SPACING.sm,
  },
  highLowText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: 2,
  },
  expandIcon: {
    marginLeft: SPACING.sm,
  },
  detailsContainer: {
    overflow: 'hidden',
    height: 80, // Fixed height instead of animated height
  },
  detailsContent: {
    padding: SPACING.md,
    paddingTop: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginTop: 2,
  },
  detailValue: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.black,
  },
});

export default WeatherWidget;
