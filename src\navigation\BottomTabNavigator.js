import React, { useEffect } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, StyleSheet, TouchableOpacity, Animated, Vibration } from 'react-native';
import { COLORS, FONT_SIZES, FONT_WEIGHTS } from '../theme/colors';

// Import screens
import DashboardScreen from '../screens/DashboardScreen';
import ServicesScreen from '../screens/ServicesScreen';
import CommunityScreen from '../screens/CommunityScreen';
import ThusongAIScreen from '../screens/ThusongAIScreen';
import CallScreen from '../screens/CallScreen';

const Tab = createBottomTabNavigator();

// Custom tab bar component
const CustomTabBar = ({ state, navigation }) => {
  // Use a fixed bottom padding instead of dynamic insets
  const bottomPadding = 10;

  return (
    <View style={[
      styles.tabBarContainer,
      {
        paddingBottom: bottomPadding,
        // Fixed height for the tab bar
        height: 80
      }
    ]}>
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          // We can use options if needed for custom configurations
          // const { options } = descriptors[route.key];
          const label = route.name;
          const isFocused = state.index === index;

          // Get icon based on tab name
          let icon = '🏠'; // Default icon
          switch (label) {
            case 'Dashboard':
              icon = '🏠';
              break;
            case 'Services':
              icon = '🏛️';
              break;
            case 'Call':
              icon = '📞';
              break;
            case 'Community':
              icon = '👥';
              break;
            case 'ThusongAI':
              icon = '🤖';
              break;
            default:
              icon = '🏠';
          }

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              // Provide haptic feedback when tab is pressed
              Vibration.vibrate(15); // Short vibration (15ms)
              navigation.navigate(route.name);
            }
          };

          return (
            <TabButton
              key={index}
              icon={icon}
              label={label === 'ThusongAI' ? 'Thusong AI' : label}
              onPress={onPress}
              isFocused={isFocused}
            />
          );
        })}
      </View>
    </View>
  );
};

// Animated Tab Button component
const TabButton = ({ icon, label, onPress, isFocused }) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isFocused ? 1 : 0,
      duration: 250,
      useNativeDriver: true,
    }).start();
  }, [isFocused, animatedValue]);

  const iconScale = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.2],
  });

  const bgOpacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const labelTranslateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -2],
  });

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.tabButton}
      onPress={onPress}
    >
      <View style={styles.tabButtonContainer}>
        <Animated.View
          style={[
            styles.tabIconBackground,
            {
              opacity: bgOpacity,
              transform: [{ scale: iconScale }],
            },
          ]}
        />
        <Animated.Text
          style={[
            styles.iconText,
            {
              transform: [{ scale: iconScale }],
            },
          ]}
        >
          {icon}
        </Animated.Text>
        <Animated.Text
          style={[
            styles.iconLabel,
            isFocused ? styles.focusedLabel : styles.unfocusedLabel,
            {
              transform: [{ translateY: labelTranslateY }],
            },
          ]}
        >
          {label}
        </Animated.Text>

        {isFocused && (
          <View style={styles.activeIndicator} />
        )}
      </View>
    </TouchableOpacity>
  );
};

// Calculate the height of the tab bar including safe area
const getTabBarHeight = () => {
  // Use a fixed height instead of dynamic insets to avoid context issues
  return 80; // Fixed height that should accommodate most devices
};

// Create a wrapper component to add bottom padding
const createScreenWithBottomPadding = (ScreenComponent) => {
  return (props) => {
    return (
      <View style={{ flex: 1 }}>
        <ScreenComponent {...props} />
        {/* This is an invisible spacer that creates room for the tab bar */}
        <View style={{ height: getTabBarHeight() }} />
      </View>
    );
  };
};

const BottomTabNavigator = () => {
  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={createScreenWithBottomPadding(DashboardScreen)}
      />
      <Tab.Screen
        name="Services"
        component={createScreenWithBottomPadding(ServicesScreen)}
      />
      <Tab.Screen
        name="Call"
        component={createScreenWithBottomPadding(CallScreen)}
      />
      <Tab.Screen
        name="Community"
        component={createScreenWithBottomPadding(CommunityScreen)}
      />
      <Tab.Screen
        name="ThusongAI"
        component={createScreenWithBottomPadding(ThusongAIScreen)}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    elevation: 0,
    zIndex: 999,
    height: 80, // Fixed height
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: 70,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 8,
    borderTopWidth: 0,
    paddingHorizontal: 10,
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
    position: 'relative',
  },
  tabIconBackground: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.offWhite,
    top: -5,
  },
  iconText: {
    fontSize: 22,
    marginBottom: 4,
    zIndex: 1,
  },
  iconLabel: {
    fontSize: FONT_SIZES.xs,
    marginTop: 2,
  },
  focusedLabel: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
    fontSize: FONT_SIZES.xs,
  },
  unfocusedLabel: {
    color: COLORS.darkGray,
    fontSize: FONT_SIZES.xs - 1,
  },
  activeIndicator: {
    position: 'absolute',
    bottom: -8,
    width: 8,
    height: 4,
    borderRadius: 2,
    backgroundColor: COLORS.primaryGreen,
  },
});

export default BottomTabNavigator;