import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';

const RDPStatusScreen = ({ navigation, route }) => {
  const [referenceNumber, setReferenceNumber] = useState('');
  const [applicationData, setApplicationData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Mock application data
  const mockApplications = {
    'RDP2024001234': {
      referenceNumber: 'RDP2024001234',
      applicantName: '<PERSON>',
      submissionDate: '2024-01-15',
      status: 'Under Review',
      statusCode: 'REVIEW',
      currentStep: 2,
      totalSteps: 5,
      estimatedCompletion: '2024-06-15',
      lastUpdate: '2024-01-20',
      documents: [
        { name: 'ID Document', status: 'Verified', icon: 'check-circle' },
        { name: 'Proof of Income', status: 'Verified', icon: 'check-circle' },
        { name: 'Proof of Address', status: 'Pending', icon: 'clock' },
        { name: 'Bank Statements', status: 'Required', icon: 'alert-circle' },
      ],
      timeline: [
        { date: '2024-01-15', status: 'Application Submitted', completed: true },
        { date: '2024-01-18', status: 'Initial Review', completed: true },
        { date: '2024-01-20', status: 'Document Verification', completed: false, current: true },
        { date: 'TBD', status: 'Site Inspection', completed: false },
        { date: 'TBD', status: 'Final Approval', completed: false },
      ],
      contactInfo: {
        officer: 'Sarah Johnson',
        phone: '************',
        email: '<EMAIL>',
      }
    },
    'RDP2024005678': {
      referenceNumber: 'RDP2024005678',
      applicantName: 'Mary Smith',
      submissionDate: '2023-11-10',
      status: 'Approved',
      statusCode: 'APPROVED',
      currentStep: 5,
      totalSteps: 5,
      estimatedCompletion: '2024-03-30',
      lastUpdate: '2024-01-10',
      allocationDetails: {
        projectName: 'Greenfield Housing Development',
        address: 'Plot 123, Greenfield Extension 2, Johannesburg',
        houseType: '2 Bedroom RDP House',
        expectedHandover: '2024-03-30',
      },
      documents: [
        { name: 'ID Document', status: 'Verified', icon: 'check-circle' },
        { name: 'Proof of Income', status: 'Verified', icon: 'check-circle' },
        { name: 'Proof of Address', status: 'Verified', icon: 'check-circle' },
        { name: 'Bank Statements', status: 'Verified', icon: 'check-circle' },
      ],
      timeline: [
        { date: '2023-11-10', status: 'Application Submitted', completed: true },
        { date: '2023-11-15', status: 'Initial Review', completed: true },
        { date: '2023-12-01', status: 'Document Verification', completed: true },
        { date: '2023-12-20', status: 'Site Inspection', completed: true },
        { date: '2024-01-10', status: 'Final Approval', completed: true, current: true },
      ],
      contactInfo: {
        officer: 'Michael Brown',
        phone: '************',
        email: '<EMAIL>',
      }
    }
  };

  useEffect(() => {
    // If coming from new application, show success message
    if (route?.params?.newApplication) {
      const newRefNumber = 'RDP2024001234';
      setReferenceNumber(newRefNumber);
      setTimeout(() => {
        searchApplication(newRefNumber);
      }, 1000);
    }
  }, [route?.params]);

  const searchApplication = (refNumber = referenceNumber) => {
    if (!refNumber.trim()) {
      Alert.alert('Error', 'Please enter a reference number');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const application = mockApplications[refNumber.toUpperCase()];
      if (application) {
        setApplicationData(application);
      } else {
        Alert.alert('Not Found', 'No application found with this reference number. Please check and try again.');
        setApplicationData(null);
      }
      setIsLoading(false);
    }, 1500);
  };

  const onRefresh = () => {
    if (applicationData) {
      setRefreshing(true);
      setTimeout(() => {
        // Simulate refreshing data
        setRefreshing(false);
      }, 1000);
    }
  };

  const getStatusColor = (statusCode) => {
    switch (statusCode) {
      case 'SUBMITTED':
        return COLORS.info;
      case 'REVIEW':
        return COLORS.warning;
      case 'APPROVED':
        return COLORS.success;
      case 'REJECTED':
        return COLORS.error;
      default:
        return COLORS.gray;
    }
  };

  const getStatusIcon = (statusCode) => {
    switch (statusCode) {
      case 'SUBMITTED':
        return 'file-document';
      case 'REVIEW':
        return 'magnify';
      case 'APPROVED':
        return 'check-circle';
      case 'REJECTED':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const renderSearchSection = () => (
    <View style={styles.searchContainer}>
      <Text style={styles.searchTitle}>Check Application Status</Text>
      <Text style={styles.searchSubtitle}>
        Enter your reference number to check your RDP housing application status
      </Text>
      
      <View style={styles.searchInputContainer}>
        <TextInput
          style={styles.searchInput}
          value={referenceNumber}
          onChangeText={setReferenceNumber}
          placeholder="Enter reference number (e.g., RDP2024001234)"
          placeholderTextColor={COLORS.gray}
          autoCapitalize="characters"
        />
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => searchApplication()}
          disabled={isLoading}
        >
          <Icon 
            name={isLoading ? "loading" : "magnify"} 
            size={20} 
            color={COLORS.white} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderApplicationStatus = () => {
    if (!applicationData) return null;

    return (
      <ScrollView
        style={styles.statusContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Status Header */}
        <View style={styles.statusHeader}>
          <View style={styles.statusIconContainer}>
            <Icon 
              name={getStatusIcon(applicationData.statusCode)} 
              size={32} 
              color={getStatusColor(applicationData.statusCode)} 
            />
          </View>
          <View style={styles.statusInfo}>
            <Text style={styles.statusTitle}>{applicationData.status}</Text>
            <Text style={styles.statusSubtitle}>
              Reference: {applicationData.referenceNumber}
            </Text>
            <Text style={styles.statusDate}>
              Last updated: {applicationData.lastUpdate}
            </Text>
          </View>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <Text style={styles.progressTitle}>Application Progress</Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${(applicationData.currentStep / applicationData.totalSteps) * 100}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            Step {applicationData.currentStep} of {applicationData.totalSteps}
          </Text>
        </View>

        {/* Timeline */}
        <View style={styles.timelineContainer}>
          <Text style={styles.sectionTitle}>Application Timeline</Text>
          {applicationData.timeline.map((item, index) => (
            <View key={index} style={styles.timelineItem}>
              <View style={styles.timelineIconContainer}>
                <View style={[
                  styles.timelineIcon,
                  {
                    backgroundColor: item.completed 
                      ? COLORS.success 
                      : item.current 
                        ? COLORS.warning 
                        : COLORS.lightGray
                  }
                ]}>
                  <Icon 
                    name={item.completed ? "check" : item.current ? "clock" : "circle"} 
                    size={16} 
                    color={COLORS.white} 
                  />
                </View>
                {index < applicationData.timeline.length - 1 && (
                  <View style={styles.timelineLine} />
                )}
              </View>
              <View style={styles.timelineContent}>
                <Text style={[
                  styles.timelineStatus,
                  item.current && styles.currentTimelineStatus
                ]}>
                  {item.status}
                </Text>
                <Text style={styles.timelineDate}>{item.date}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Documents */}
        <View style={styles.documentsContainer}>
          <Text style={styles.sectionTitle}>Required Documents</Text>
          {applicationData.documents.map((doc, index) => (
            <View key={index} style={styles.documentItem}>
              <Icon 
                name={doc.icon} 
                size={20} 
                color={
                  doc.status === 'Verified' 
                    ? COLORS.success 
                    : doc.status === 'Pending' 
                      ? COLORS.warning 
                      : COLORS.error
                } 
              />
              <View style={styles.documentInfo}>
                <Text style={styles.documentName}>{doc.name}</Text>
                <Text style={[
                  styles.documentStatus,
                  {
                    color: doc.status === 'Verified' 
                      ? COLORS.success 
                      : doc.status === 'Pending' 
                        ? COLORS.warning 
                        : COLORS.error
                  }
                ]}>
                  {doc.status}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Allocation Details (if approved) */}
        {applicationData.statusCode === 'APPROVED' && applicationData.allocationDetails && (
          <View style={styles.allocationContainer}>
            <Text style={styles.sectionTitle}>Housing Allocation</Text>
            <View style={styles.allocationCard}>
              <View style={styles.allocationItem}>
                <Text style={styles.allocationLabel}>Project:</Text>
                <Text style={styles.allocationValue}>{applicationData.allocationDetails.projectName}</Text>
              </View>
              <View style={styles.allocationItem}>
                <Text style={styles.allocationLabel}>Address:</Text>
                <Text style={styles.allocationValue}>{applicationData.allocationDetails.address}</Text>
              </View>
              <View style={styles.allocationItem}>
                <Text style={styles.allocationLabel}>House Type:</Text>
                <Text style={styles.allocationValue}>{applicationData.allocationDetails.houseType}</Text>
              </View>
              <View style={styles.allocationItem}>
                <Text style={styles.allocationLabel}>Expected Handover:</Text>
                <Text style={styles.allocationValue}>{applicationData.allocationDetails.expectedHandover}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Contact Information */}
        <View style={styles.contactContainer}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          <View style={styles.contactCard}>
            <Text style={styles.contactOfficer}>
              Case Officer: {applicationData.contactInfo.officer}
            </Text>
            <TouchableOpacity style={styles.contactItem}>
              <Icon name="phone" size={20} color={COLORS.primaryGreen} />
              <Text style={styles.contactText}>{applicationData.contactInfo.phone}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.contactItem}>
              <Icon name="email" size={20} color={COLORS.primaryGreen} />
              <Text style={styles.contactText}>{applicationData.contactInfo.email}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Application Status</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        {renderSearchSection()}
        {renderApplicationStatus()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.offWhite,
  },
  header: {
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    margin: SPACING.md,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.xs,
  },
  searchSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray,
    marginBottom: SPACING.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 8,
    padding: SPACING.sm,
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginRight: SPACING.sm,
  },
  searchButton: {
    backgroundColor: COLORS.primaryGreen,
    padding: SPACING.sm,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
  },
  statusContainer: {
    flex: 1,
    padding: SPACING.md,
  },
  statusHeader: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusIconContainer: {
    marginRight: SPACING.md,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  statusSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray,
    marginTop: 2,
  },
  statusDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
    marginTop: 2,
  },
  progressContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  progressTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.sm,
  },
  progressBar: {
    height: 8,
    backgroundColor: COLORS.lightGray,
    borderRadius: 4,
    marginBottom: SPACING.xs,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 4,
  },
  progressText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray,
    textAlign: 'center',
  },
  timelineContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.md,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: SPACING.sm,
  },
  timelineIconContainer: {
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: COLORS.lightGray,
    marginTop: 4,
  },
  timelineContent: {
    flex: 1,
    paddingTop: 4,
  },
  timelineStatus: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.primaryGreen,
  },
  currentTimelineStatus: {
    fontWeight: FONT_WEIGHTS.bold,
  },
  timelineDate: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
    marginTop: 2,
  },
  documentsContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  documentInfo: {
    flex: 1,
    marginLeft: SPACING.sm,
  },
  documentName: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.primaryGreen,
  },
  documentStatus: {
    fontSize: FONT_SIZES.xs,
    marginTop: 2,
  },
  allocationContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  allocationCard: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 8,
    padding: SPACING.sm,
  },
  allocationItem: {
    marginBottom: SPACING.sm,
  },
  allocationLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray,
    marginBottom: 2,
  },
  allocationValue: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.primaryGreen,
  },
  contactContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  contactCard: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 8,
    padding: SPACING.sm,
  },
  contactOfficer: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.sm,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  contactText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    marginLeft: SPACING.sm,
  },
});

export default RDPStatusScreen;
