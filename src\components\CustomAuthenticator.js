import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Authenticator, ThemeProvider } from '@aws-amplify/ui-react-native';
import { Auth } from 'aws-amplify';
import { COLORS, FONT_SIZES, FONT_WEIGHTS } from '../theme/colors';
import { authenticatorTheme } from '../theme/authenticatorTheme';

// Custom Header component for the Authenticator
const CustomHeader = () => (
  <View style={styles.header}>
    <Text style={styles.appName}>Let's Talk</Text>
    <Text style={styles.tagline}>Connect with your community</Text>
  </View>
);

// Custom Footer component for the Authenticator
const CustomFooter = () => (
  <View style={styles.footer}>
    <Text style={styles.footerText}>© 2023 Let's Talk. All rights reserved.</Text>
  </View>
);

// Custom SignIn component that uses ID Number instead of email
const CustomSignIn = (props) => {
  // Override the default SignIn component to use ID Number
  return (
    <Authenticator.SignIn
      {...props}
      Header={() => (
        <View style={styles.signInHeader}>
          <Text style={styles.signInHeaderText}>Sign In</Text>
          <Text style={styles.signInSubText}>Use your ID Number to sign in</Text>
        </View>
      )}
    />
  );
};

// Custom SignUp component that uses ID Number instead of email
const CustomSignUp = (props) => {
  // Override the default SignUp component to use ID Number
  return (
    <Authenticator.SignUp
      {...props}
      Header={() => (
        <View style={styles.signUpHeader}>
          <Text style={styles.signUpHeaderText}>Create Account</Text>
          <Text style={styles.signUpSubText}>Sign up with your ID Number</Text>
        </View>
      )}
    />
  );
};

// Main CustomAuthenticator component
const CustomAuthenticator = ({ children }) => {
  // Custom services to override the default Amplify Auth functions
  const services = {
    // Override the signUp function to use direct Auth API
    handleSignUp: async ({ username, password, options }) => {
      // Extract attributes from options
      const { userAttributes } = options || {};
      const { name, email } = userAttributes || {};

      try {
        // Call Auth.signUp directly
        const { user } = await Auth.signUp({
          username: username, // ID Number
          password,
          attributes: {
            email,
            name,
            'custom:idNumber': username // Store ID Number as a custom attribute
          }
        });

        return user;
      } catch (error) {
        console.error('Error signing up:', error);
        throw error;
      }
    },

    // Override the signIn function to use direct Auth API
    handleSignIn: async ({ username, password }) => {
      try {
        // Call Auth.signIn directly
        const user = await Auth.signIn(username, password);
        return user;
      } catch (error) {
        console.error('Error signing in:', error);
        throw error;
      }
    },

    // Override the confirmSignUp function to use direct Auth API
    handleConfirmSignUp: async ({ username, confirmationCode }) => {
      try {
        // Call Auth.confirmSignUp directly
        await Auth.confirmSignUp(username, confirmationCode);
        return true;
      } catch (error) {
        console.error('Error confirming sign up:', error);
        throw error;
      }
    },

    // Override the signOut function to use direct Auth API
    handleSignOut: async () => {
      try {
        // Call Auth.signOut directly
        await Auth.signOut();
        return true;
      } catch (error) {
        console.error('Error signing out:', error);
        throw error;
      }
    },
  };

  // Custom form fields for SignUp to include ID Number
  const formFields = {
    signUp: {
      username: {
        label: 'ID Number',
        placeholder: 'Enter your 13-digit ID Number',
        order: 1,
      },
      name: {
        label: 'Full Name',
        placeholder: 'Enter your full name',
        order: 2,
      },
      email: {
        label: 'Email',
        placeholder: 'Enter your email address',
        order: 3,
      },
      password: {
        label: 'Password',
        placeholder: 'Create a password',
        order: 4,
      },
      confirm_password: {
        label: 'Confirm Password',
        placeholder: 'Confirm your password',
        order: 5,
      },
    },
    signIn: {
      username: {
        label: 'ID Number',
        placeholder: 'Enter your ID Number',
      },
      password: {
        label: 'Password',
        placeholder: 'Enter your password',
      },
    },
  };

  return (
    <ThemeProvider theme={authenticatorTheme}>
      <Authenticator.Provider>
        <Authenticator
          // Apply custom styling to the container
          Container={(props) => (
            <Authenticator.Container
              {...props}
              style={styles.container}
            />
          )}
          // Use custom header and footer
          Header={CustomHeader}
          Footer={CustomFooter}
          // Override specific components
          components={{
            SignIn: CustomSignIn,
            SignUp: CustomSignUp,
          }}
          // Use custom services
          services={services}
          // Use custom form fields
          formFields={formFields}
        >
          {children}
        </Authenticator>
      </Authenticator.Provider>
    </ThemeProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 20,
  },
  appName: {
    fontSize: FONT_SIZES.title,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: 8,
  },
  tagline: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  footerText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  signInHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  signInHeaderText: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 8,
  },
  signInSubText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
  signUpHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  signUpHeaderText: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 8,
  },
  signUpSubText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
});

export default CustomAuthenticator;
