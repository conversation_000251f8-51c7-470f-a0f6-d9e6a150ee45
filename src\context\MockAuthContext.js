import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Sample users for mock authentication
const MOCK_USERS = [
  {
    id: '1',
    idNumber: '9001015678083',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'citizen'
  },
  {
    id: '2',
    idNumber: '8502124567084',
    name: '<PERSON><PERSON><PERSON>dlovu',
    email: '<EMAIL>',
    password: 'password123',
    role: 'citizen'
  },
  {
    id: '3',
    idNumber: '7605125678085',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    department: 'Customer Support'
  },
  {
    id: '4',
    idNumber: '7312154567086',
    name: '<PERSON><PERSON>lo<PERSON>',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    department: 'Support Management'
  },
  {
    id: '5',
    idNumber: '6501015678087',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    password: 'doctor123',
    role: 'doctor',
    specialty: 'General Practitioner',
    location: 'Central City Clinic',
    licenseNumber: 'MD12345'
  },
  {
    id: '6',
    idNumber: '7002024567088',
    name: 'Dr. Michael Ndlovu',
    email: '<EMAIL>',
    password: 'doctor123',
    role: 'doctor',
    specialty: 'Pediatrician',
    location: 'Sunshine Children\'s Clinic',
    licenseNumber: 'MD67890'
  }
];

// Create the Auth Context
const MockAuthContext = createContext();

// Custom hook to use the Auth Context
export const useMockAuth = () => {
  return useContext(MockAuthContext);
};

// Auth Provider component
export const MockAuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(null);
  const [needsConfirmation, setNeedsConfirmation] = useState(false);
  const [tempIdNumber, setTempIdNumber] = useState('');

  // Check for current user on mount
  useEffect(() => {
    const checkUser = async () => {
      try {
        const userJson = await AsyncStorage.getItem('@auth_user');
        if (userJson) {
          const user = JSON.parse(userJson);
          console.log('Found user in AsyncStorage:', user);
          setCurrentUser(user);
        } else {
          console.log('No user found in AsyncStorage');
        }
      } catch (error) {
        console.error('Error checking for authenticated user:', error);
      } finally {
        setLoading(false);
      }
    };

    checkUser();
  }, []);

  // Mock sign in
  const signIn = async (idNumber, password) => {
    setLoading(true);
    setAuthError(null);

    try {
      console.log('Attempting to sign in with ID:', idNumber);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user by ID number
      const user = MOCK_USERS.find(u => u.idNumber === idNumber);
      console.log('User found:', user ? 'Yes' : 'No');

      if (!user) {
        console.error('Login failed: User not found');
        throw new Error('User not found');
      }

      if (user.password !== password) {
        console.error('Login failed: Incorrect password');
        throw new Error('Incorrect password');
      }

      // Remove password from user object before storing
      const { password: _, ...userWithoutPassword } = user;
      console.log('User authenticated successfully:', userWithoutPassword);

      try {
        // Store user in AsyncStorage
        await AsyncStorage.setItem('@auth_user', JSON.stringify(userWithoutPassword));
        console.log('User saved to AsyncStorage');
      } catch (storageError) {
        console.error('Failed to save user to AsyncStorage:', storageError);
        // Continue even if storage fails
      }

      // Update state
      setCurrentUser(userWithoutPassword);
      console.log('Current user state updated');

      return { success: true, user: userWithoutPassword };
    } catch (error) {
      console.error('Login error:', error.message);
      setAuthError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Mock sign up
  const signUp = async (idNumber, password, name, email) => {
    setLoading(true);
    setAuthError(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if user already exists
      if (MOCK_USERS.some(u => u.idNumber === idNumber)) {
        throw new Error('User with this ID number already exists');
      }

      if (MOCK_USERS.some(u => u.email === email)) {
        throw new Error('User with this email already exists');
      }

      // Create new user
      const newUser = {
        id: String(MOCK_USERS.length + 1),
        idNumber,
        name,
        email,
        password
      };

      // Add to mock users (in a real app, this would be a database operation)
      MOCK_USERS.push(newUser);

      // Set needs confirmation to simulate verification process
      setNeedsConfirmation(true);
      setTempIdNumber(idNumber);

      return { success: true };
    } catch (error) {
      setAuthError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Mock confirm sign up
  const confirmSignUp = async (idNumber, code) => {
    setLoading(true);
    setAuthError(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a mock environment, any code is valid
      if (code.length !== 6) {
        throw new Error('Verification code must be 6 digits');
      }

      // Find the user
      const user = MOCK_USERS.find(u => u.idNumber === idNumber);

      if (!user) {
        throw new Error('User not found');
      }

      // Reset confirmation state
      setNeedsConfirmation(false);
      setTempIdNumber('');

      return { success: true };
    } catch (error) {
      setAuthError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Mock sign out
  const signOut = async () => {
    setLoading(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Remove user from AsyncStorage
      await AsyncStorage.removeItem('@auth_user');

      setCurrentUser(null);
      return { success: true };
    } catch (error) {
      setAuthError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Mock get current user
  const getCurrentUser = async () => {
    try {
      if (currentUser) {
        return { success: true, user: currentUser };
      }

      const userJson = await AsyncStorage.getItem('@auth_user');
      if (userJson) {
        const user = JSON.parse(userJson);
        setCurrentUser(user);
        return { success: true, user };
      }

      return { success: false, error: 'No authenticated user' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Mock resend verification code
  const resendVerificationCode = async (idNumber) => {
    setLoading(true);
    setAuthError(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find the user
      const user = MOCK_USERS.find(u => u.idNumber === idNumber);

      if (!user) {
        throw new Error('User not found');
      }

      // Set needs confirmation
      setNeedsConfirmation(true);
      setTempIdNumber(idNumber);

      return { success: true };
    } catch (error) {
      setAuthError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Reset confirmation state
  const resetConfirmation = () => {
    setNeedsConfirmation(false);
    setTempIdNumber('');
  };

  // Clear error
  const clearError = () => {
    setAuthError(null);
  };

  // Context value
  const value = {
    currentUser,
    loading,
    authError,
    needsConfirmation,
    tempIdNumber,
    signIn,
    signUp,
    confirmSignUp,
    signOut,
    getCurrentUser,
    resendVerificationCode,
    resetConfirmation,
    clearError
  };

  return (
    <MockAuthContext.Provider value={value}>
      {children}
    </MockAuthContext.Provider>
  );
};

export default MockAuthContext;
